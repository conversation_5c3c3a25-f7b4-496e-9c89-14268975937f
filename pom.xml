<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sinoyd.parse.webbackend</groupId>
    <artifactId>parseWebbackend</artifactId>
    <version>0.0.2-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>parseWebbackend</name>
    <description>框架启动项</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>

    <parent>
        <groupId>com.sinoydframework.boot</groupId>
        <artifactId>sinoyd-boot-starter-parent</artifactId>
        <version>5.2.1-SNAPSHOT</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>com.sinoyd.parse</groupId>
            <artifactId>sinoyd-parse-impl</artifactId>
            <version>0.0.2Cloud-SNAPSHOT</version>
<!--            <groupId>com.sinoyd.frame</groupId>-->
<!--            <artifactId>frame-arch</artifactId>-->
<!--            <version>6.0.0boot-SNAPSHOT</version>-->
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.csp</groupId>
                    <artifactId>sentinel-datasource-nacos</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sinoyd.parse</groupId>
            <artifactId>sinoyd-parse-grpc</artifactId>
            <version>0.0.2Cloud-SNAPSHOT</version>
        </dependency>
    </dependencies>


    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>7.0.0.jre8</version>
                <scope>compile</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <finalName>sinoydParseWebbackend</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


    <repositories>
        <repository>
            <id>maven-central</id>
            <name>maven-central</name>
            <url>http://nexusproxy.dev.yd/repository/maven-public/</url>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>
    <distributionManagement>
        <snapshotRepository>
            <id>nexus</id><!--这里需要和settings.xml中配置的私有库名称一致-->
            <name>Nexus Snapshot</name>
            <url>http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </snapshotRepository>
        <site>
            <id>nexus</id>
            <name>Nexus Sites</name>
            <url>dav:http://nexusproxy.dev.yd/repository/maven-snapshots/</url>
        </site>
    </distributionManagement>
</project>