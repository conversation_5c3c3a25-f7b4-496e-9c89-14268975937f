2025-09-10 08:19:35.694  WARN 2072 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[shared-redis.yaml] & group[SHARED_REDIS]
2025-09-10 08:19:35.725  WARN 2072 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse] & group[DEFAULT_GROUP]
2025-09-10 08:19:35.733  WARN 2072 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse.yaml] & group[DEFAULT_GROUP]
2025-09-10 08:19:35.739  WARN 2072 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse-dev.yaml] & group[DEFAULT_GROUP]
2025-09-10 08:19:35.741  INFO 2072 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-shared-redis.yaml,SHARED_REDIS'}]
2025-09-10 08:19:35.783  INFO 2072 --- [main] c.sinoyd.EnableEurekaServerApplication   : The following 1 profile is active: "dev"
2025-09-10 08:19:39.672  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 08:19:39.673  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 08:19:40.163  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 472 ms. Found 21 JPA repository interfaces.
2025-09-10 08:19:40.206  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 08:19:40.208  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 08:19:40.258  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiInstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.259  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiParseFileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.260  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AppFlowDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.260  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.262  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasTestRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.262  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ErrorLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.263  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.264  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.265  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileFlowRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.265  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.266  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileResultAlaisRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.267  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.InstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.267  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.OrgStreamRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.268  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseDocumentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.268  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.270  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.272  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.272  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.273  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamErrLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.274  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.274  INFO 2072 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 08:19:40.274  INFO 2072 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
2025-09-10 08:19:41.320  INFO 2072 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=bf49610d-670f-392d-8457-df37c8b6197a
2025-09-10 08:19:42.866  INFO 2072 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'cacheManagerConfig' of type [com.sinoyd.frame.base.configuration.CacheManagerConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-10 08:19:43.976  INFO 2072 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8980 (http)
2025-09-10 08:19:44.004  INFO 2072 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-10 08:19:44.004  INFO 2072 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-09-10 08:19:44.191  INFO 2072 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-10 08:19:44.191  INFO 2072 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 8320 ms
2025-09-10 08:19:44.912  INFO 2072 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Starting...
2025-09-10 08:19:45.219  INFO 2072 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Start completed.
2025-09-10 08:19:45.464  INFO 2072 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 08:19:45.600  INFO 2072 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-10 08:19:45.985  INFO 2072 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-10 08:19:46.361  INFO 2072 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 08:19:47.989  INFO 2072 --- [main] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 08:19:48.410  INFO 2072 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 08:19:48.448  INFO 2072 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 08:19:49.077  WARN 2072 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-10 08:19:49.588  INFO 2072 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-10 08:19:55.250  INFO 2072 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@33ffbde5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6d194f34, org.springframework.security.web.context.SecurityContextPersistenceFilter@3468198d, org.springframework.security.web.header.HeaderWriterFilter@3c1ab0e1, org.springframework.web.filter.CorsFilter@14b7d29e, org.springframework.security.web.authentication.logout.LogoutFilter@ea3a636, com.sinoyd.frame.filter.JwtUserAuthFilter@408e47f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2d935682, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@54724e71, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ad6618e, org.springframework.security.web.session.SessionManagementFilter@1c553a85, org.springframework.security.web.access.ExceptionTranslationFilter@fbd0581, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6258accb]
2025-09-10 08:19:57.588  WARN 2072 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-10 08:19:57.786  INFO 2072 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8980 (http) with context path ''
2025-09-10 08:19:58.040  INFO 2072 --- [main] c.sinoyd.EnableEurekaServerApplication   : Started EnableEurekaServerApplication in 27.679 seconds (JVM running for 33.479)
2025-09-10 08:19:58.070  INFO 2072 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse-dev.yaml, group=DEFAULT_GROUP
2025-09-10 08:19:58.071  INFO 2072 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse, group=DEFAULT_GROUP
2025-09-10 08:19:58.071  INFO 2072 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=shared-redis.yaml, group=SHARED_REDIS
2025-09-10 08:19:58.071  INFO 2072 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse.yaml, group=DEFAULT_GROUP
2025-09-10 08:19:58.910  INFO 2072 --- [main] com.sinoyd.grpc.start.GrpcStartService   : Server started, listening on 8910
2025-09-10 08:28:50.992  INFO 2072 --- [http-nio-8980-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-10 08:28:50.993  INFO 2072 --- [http-nio-8980-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-10 08:28:50.996  INFO 2072 --- [http-nio-8980-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-09-10 09:47:23.710  INFO 2072 --- [http-nio-8980-exec-9] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 09:47:23.728  INFO 2072 --- [http-nio-8980-exec-9] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 09:47:23.919 ERROR 2072 --- [pool-6-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析程序异常：

java.lang.IllegalArgumentException: Invalid URL port: "1212121"
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1329) ~[okhttp-4.9.3.jar:na]
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1633) ~[okhttp-4.9.3.jar:na]
	at okhttp3.Request$Builder.url(Request.kt:184) ~[okhttp-4.9.3.jar:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.doStreamRequest(AiParseFileAppServiceImpl.java:394) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:298) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:47:23.919 ERROR 2072 --- [pool-6-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析程序异常：

java.lang.IllegalArgumentException: Invalid URL port: "1212121"
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1329) ~[okhttp-4.9.3.jar:na]
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1633) ~[okhttp-4.9.3.jar:na]
	at okhttp3.Request$Builder.url(Request.kt:184) ~[okhttp-4.9.3.jar:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.doStreamRequest(AiParseFileAppServiceImpl.java:394) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:298) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:47:23.961  INFO 2072 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 09:47:23.961  INFO 2072 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 0
2025-09-10 09:48:00.336  INFO 2072 --- [http-nio-8980-exec-2] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 09:48:00.337  INFO 2072 --- [http-nio-8980-exec-2] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 09:48:00.451 ERROR 2072 --- [pool-7-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析程序异常：

java.lang.IllegalArgumentException: Invalid URL port: "1212121"
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1329) ~[okhttp-4.9.3.jar:na]
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1633) ~[okhttp-4.9.3.jar:na]
	at okhttp3.Request$Builder.url(Request.kt:184) ~[okhttp-4.9.3.jar:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.doStreamRequest(AiParseFileAppServiceImpl.java:394) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:298) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:48:00.451 ERROR 2072 --- [pool-7-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析程序异常：

java.lang.IllegalArgumentException: Invalid URL port: "1212121"
	at okhttp3.HttpUrl$Builder.parse$okhttp(HttpUrl.kt:1329) ~[okhttp-4.9.3.jar:na]
	at okhttp3.HttpUrl$Companion.get(HttpUrl.kt:1633) ~[okhttp-4.9.3.jar:na]
	at okhttp3.Request$Builder.url(Request.kt:184) ~[okhttp-4.9.3.jar:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.doStreamRequest(AiParseFileAppServiceImpl.java:394) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:298) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:48:00.509  INFO 2072 --- [http-nio-8980-exec-3] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 09:48:00.509  INFO 2072 --- [http-nio-8980-exec-3] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 1
2025-09-10 09:48:37.064  WARN 2072 --- [http-nio-8980-exec-10] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@312d0bfc (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 09:48:37.068  WARN 2072 --- [http-nio-8980-exec-10] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@4969c7fd (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 09:51:16.652  INFO 2072 --- [http-nio-8980-exec-9] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 09:51:16.663  INFO 2072 --- [http-nio-8980-exec-9] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-09-10 09:51:16.673  INFO 2072 --- [http-nio-8980-exec-9] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 09:51:16.678  INFO 2072 --- [http-nio-8980-exec-9] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 JPA repository interfaces.
2025-09-10 09:51:17.528  INFO 2072 --- [http-nio-8980-exec-10] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 09:51:17.546  INFO 2072 --- [http-nio-8980-exec-10] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 09:51:17.984  INFO 2072 --- [http-nio-8980-exec-10] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 09:51:18.211  INFO 2072 --- [http-nio-8980-exec-10] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 09:55:20.286  INFO 2072 --- [http-nio-8980-exec-3] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 09:55:20.289  INFO 2072 --- [http-nio-8980-exec-3] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 09:55:22.488 ERROR 2072 --- [pool-8-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口请求失败：

java.io.IOException: closed
	at okio.RealBufferedSource$inputStream$1.read(RealBufferedSource.kt:154) ~[okio-2.8.0.jar:na]
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178) ~[na:1.8.0_462]
	at java.io.InputStreamReader.read(InputStreamReader.java:184) ~[na:1.8.0_462]
	at java.io.BufferedReader.fill(BufferedReader.java:161) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:324) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:389) ~[na:1.8.0_462]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:300) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:55:24.397 ERROR 2072 --- [pool-8-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口请求失败：

java.io.IOException: closed
	at okio.RealBufferedSource$inputStream$1.read(RealBufferedSource.kt:154) ~[okio-2.8.0.jar:na]
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178) ~[na:1.8.0_462]
	at java.io.InputStreamReader.read(InputStreamReader.java:184) ~[na:1.8.0_462]
	at java.io.BufferedReader.fill(BufferedReader.java:161) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:324) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:389) ~[na:1.8.0_462]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:300) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:55:24.405  INFO 2072 --- [http-nio-8980-exec-10] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 09:55:24.405  INFO 2072 --- [http-nio-8980-exec-10] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 2
2025-09-10 09:57:19.536  INFO 2072 --- [http-nio-8980-exec-9] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 
2025-09-10 09:57:19.537  INFO 2072 --- [http-nio-8980-exec-9] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 
2025-09-10 09:57:21.875 ERROR 2072 --- [pool-9-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口请求失败：

java.io.IOException: closed
	at okio.RealBufferedSource$inputStream$1.read(RealBufferedSource.kt:154) ~[okio-2.8.0.jar:na]
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178) ~[na:1.8.0_462]
	at java.io.InputStreamReader.read(InputStreamReader.java:184) ~[na:1.8.0_462]
	at java.io.BufferedReader.fill(BufferedReader.java:161) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:324) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:389) ~[na:1.8.0_462]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:300) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 09:57:21.884  INFO 2072 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 
2025-09-10 09:57:21.884  INFO 2072 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 3
2025-09-10 10:05:59.580  WARN 2072 --- [Thread-19] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-10 10:05:59.586  WARN 2072 --- [Thread-27] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-09-10 10:05:59.586  WARN 2072 --- [Thread-27] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-09-10 10:05:59.599  WARN 2072 --- [Thread-19] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-09-10 10:06:00.046  INFO 2072 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 10:06:00.048  INFO 2072 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown initiated...
2025-09-10 10:06:00.054  INFO 2072 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown completed.
2025-09-10 10:06:09.288  WARN 14944 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[shared-redis.yaml] & group[SHARED_REDIS]
2025-09-10 10:06:09.298  WARN 14944 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse] & group[DEFAULT_GROUP]
2025-09-10 10:06:09.306  WARN 14944 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse.yaml] & group[DEFAULT_GROUP]
2025-09-10 10:06:09.314  WARN 14944 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse-dev.yaml] & group[DEFAULT_GROUP]
2025-09-10 10:06:09.315  INFO 14944 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-shared-redis.yaml,SHARED_REDIS'}]
2025-09-10 10:06:09.342  INFO 14944 --- [main] c.sinoyd.EnableEurekaServerApplication   : The following 1 profile is active: "dev"
2025-09-10 10:06:10.726  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 10:06:10.726  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 10:06:10.996  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 259 ms. Found 21 JPA repository interfaces.
2025-09-10 10:06:11.015  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 10:06:11.018  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 10:06:11.058  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiInstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.059  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiParseFileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.059  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AppFlowDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.060  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.060  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasTestRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.061  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ErrorLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.061  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.062  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.062  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileFlowRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.063  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.063  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileResultAlaisRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.064  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.InstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.064  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.OrgStreamRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.065  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseDocumentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.066  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.067  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.068  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.068  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.069  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamErrLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.069  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.069  INFO 14944 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 10:06:11.070  INFO 14944 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
2025-09-10 10:06:11.634  INFO 14944 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=bf49610d-670f-392d-8457-df37c8b6197a
2025-09-10 10:06:12.147  INFO 14944 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'cacheManagerConfig' of type [com.sinoyd.frame.base.configuration.CacheManagerConfig$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-10 10:06:12.784  INFO 14944 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8980 (http)
2025-09-10 10:06:12.802  INFO 14944 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-10 10:06:12.802  INFO 14944 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-09-10 10:06:12.957  INFO 14944 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-10 10:06:12.957  INFO 14944 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3585 ms
2025-09-10 10:06:13.447  INFO 14944 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Starting...
2025-09-10 10:06:13.669  INFO 14944 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Start completed.
2025-09-10 10:06:13.818  INFO 14944 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 10:06:13.892  INFO 14944 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-10 10:06:14.123  INFO 14944 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-10 10:06:14.447  INFO 14944 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 10:06:15.755  INFO 14944 --- [main] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 10:06:16.024  INFO 14944 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 10:06:16.053  INFO 14944 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 10:06:16.476  WARN 14944 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-10 10:06:16.953  INFO 14944 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-10 10:06:21.724  INFO 14944 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@4d2acd3c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@181ab814, org.springframework.security.web.context.SecurityContextPersistenceFilter@4f0ee1eb, org.springframework.security.web.header.HeaderWriterFilter@280b0df9, org.springframework.web.filter.CorsFilter@6eded737, org.springframework.security.web.authentication.logout.LogoutFilter@6c6664c4, com.sinoyd.frame.filter.JwtUserAuthFilter@44667128, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1ec30f25, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6ed256b2, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5f3e07b5, org.springframework.security.web.session.SessionManagementFilter@2126610, org.springframework.security.web.access.ExceptionTranslationFilter@3f0938c4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@785877ec]
2025-09-10 10:06:23.558  WARN 14944 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-10 10:06:23.732  INFO 14944 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8980 (http) with context path ''
2025-09-10 10:06:23.902  INFO 14944 --- [main] c.sinoyd.EnableEurekaServerApplication   : Started EnableEurekaServerApplication in 18.085 seconds (JVM running for 21.531)
2025-09-10 10:06:23.920  INFO 14944 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse-dev.yaml, group=DEFAULT_GROUP
2025-09-10 10:06:23.920  INFO 14944 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse, group=DEFAULT_GROUP
2025-09-10 10:06:23.921  INFO 14944 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=shared-redis.yaml, group=SHARED_REDIS
2025-09-10 10:06:23.921  INFO 14944 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse.yaml, group=DEFAULT_GROUP
2025-09-10 10:06:24.396  INFO 14944 --- [main] com.sinoyd.grpc.start.GrpcStartService   : Server started, listening on 8910
2025-09-10 10:24:17.893  INFO 14944 --- [http-nio-8980-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-10 10:24:17.893  INFO 14944 --- [http-nio-8980-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-09-10 10:24:17.896  INFO 14944 --- [http-nio-8980-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-09-10 10:24:25.810  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 10:24:25.826  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 10:24:28.015 ERROR 14944 --- [pool-5-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口请求失败：

java.io.IOException: closed
	at okio.RealBufferedSource$inputStream$1.read(RealBufferedSource.kt:154) ~[okio-2.8.0.jar:na]
	at sun.nio.cs.StreamDecoder.readBytes(StreamDecoder.java:284) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.implRead(StreamDecoder.java:326) ~[na:1.8.0_462]
	at sun.nio.cs.StreamDecoder.read(StreamDecoder.java:178) ~[na:1.8.0_462]
	at java.io.InputStreamReader.read(InputStreamReader.java:184) ~[na:1.8.0_462]
	at java.io.BufferedReader.fill(BufferedReader.java:161) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:324) ~[na:1.8.0_462]
	at java.io.BufferedReader.readLine(BufferedReader.java:389) ~[na:1.8.0_462]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:300) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:24:28.058  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 0, 用户ID: 
2025-09-10 10:24:28.059  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 0
2025-09-10 10:27:52.282  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 10:27:52.292  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-09-10 10:27:52.295  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 10:27:52.300  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 0 JPA repository interfaces.
2025-09-10 10:27:52.752  INFO 14944 --- [http-nio-8980-exec-10] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 10:27:52.771  INFO 14944 --- [http-nio-8980-exec-10] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 10:27:53.198  INFO 14944 --- [http-nio-8980-exec-10] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 10:27:53.440  INFO 14944 --- [http-nio-8980-exec-10] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 10:28:37.027  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 10:28:37.030  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 10:29:45.873  WARN 14944 --- [instrumentparse52 housekeeper] com.zaxxer.hikari.pool.HikariPool        : instrumentparse52 - Thread starvation or clock leap detected (housekeeper delta=1m1s700ms331µs401ns).
2025-09-10 10:29:45.913 ERROR 14944 --- [http-nio-8980-exec-3] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception

com.sinoyd.boot.common.exception.BaseException: 请求未授权，不允许进行此操作
	at com.sinoyd.frame.filter.JwtUserAuthFilter.getAuthentication(JwtUserAuthFilter.java:110) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at com.sinoyd.frame.filter.JwtUserAuthFilter.doFilterInternal(JwtUserAuthFilter.java:75) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_462]

2025-09-10 10:29:45.913 ERROR 14944 --- [http-nio-8980-exec-9] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception

com.sinoyd.boot.common.exception.BaseException: 请求未授权，不允许进行此操作
	at com.sinoyd.frame.filter.JwtUserAuthFilter.getAuthentication(JwtUserAuthFilter.java:110) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at com.sinoyd.frame.filter.JwtUserAuthFilter.doFilterInternal(JwtUserAuthFilter.java:75) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_462]

2025-09-10 10:29:45.913 ERROR 14944 --- [http-nio-8980-exec-4] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception

com.sinoyd.boot.common.exception.BaseException: 请求未授权，不允许进行此操作
	at com.sinoyd.frame.filter.JwtUserAuthFilter.getAuthentication(JwtUserAuthFilter.java:110) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at com.sinoyd.frame.filter.JwtUserAuthFilter.doFilterInternal(JwtUserAuthFilter.java:75) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_462]

2025-09-10 10:29:45.913 ERROR 14944 --- [http-nio-8980-exec-10] o.a.c.c.C.[.[.[/].[dispatcherServlet]    : Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception

com.sinoyd.boot.common.exception.BaseException: 请求未授权，不允许进行此操作
	at com.sinoyd.frame.filter.JwtUserAuthFilter.getAuthentication(JwtUserAuthFilter.java:110) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at com.sinoyd.frame.filter.JwtUserAuthFilter.doFilterInternal(JwtUserAuthFilter.java:75) ~[frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) ~[spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) ~[spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) ~[tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_462]

2025-09-10 10:29:45.991  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 1, 用户ID: 
2025-09-10 10:29:45.992  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 1
2025-09-10 10:30:01.168  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 10:30:01.169  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 10:30:48.263 ERROR 14944 --- [pool-7-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: syntax error, pos 1, line 1, column 2```json
{
  "参数列表": [
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样开始时间",
      "参数值": "1336",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样结束时间",
      "参数值": "1436",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样时间",
      "参数值": "60",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样流量",
      "参数值": "0.5",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "累计体积",
      "参数值": "30.00",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "标况体积",
      "参数值": "26.98",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "参比体积",
      "参数值": "29.45",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "计前温度",
      "参数值": "31.0",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "计前压力",
      "参数值": "-1.42",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样开始时间",
      "参数值": "1336",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样结束时间",
      "参数值": "1436",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样时间",
      "参数值": "60",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "采样流量",
      "参数值": "1.0",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "累计体积",
      "参数值": "59.94",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "标况体积",
      "参数值": "53.91",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "参比体积",
      "参数值": "58.84",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "计前温度",
      "参数值": "31.4",
      "量纲": ""
    },
    {
      "样品编号": "SY2024082101",
      "分析因子": "F",
      "参数名称": "计前压力",
      "参数值": "-1.74",
      "量纲": ""
    }
  ]
}
```
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1510) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1390) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:181) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:30:48.267 ERROR 14944 --- [pool-7-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:30:48.275  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 2, 用户ID: 
2025-09-10 10:30:48.275  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 2
2025-09-10 10:31:51.565  WARN 14944 --- [http-nio-8980-exec-2] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@5d687752 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 10:46:28.782  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:46:28.784  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:46:28.834 ERROR 14944 --- [http-nio-8980-exec-7] c.s.f.c.ExceptionHandlerController       : 存在未上传附件的应用，请先上传附件再进行解析！

com.sinoyd.boot.common.exception.BaseException: 存在未上传附件的应用，请先上传附件再进行解析！
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.batchParse(AiParseFileAppServiceImpl.java:134) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl$$EnhancerBySpringCGLIB$$1.batchParse(<generated>) ~[classes/:na]
	at com.sinoyd.parse.controller.AiParseFileAppController.batchParseWS(AiParseFileAppController.java:100) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_462]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_462]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_462]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.20.jar:5.3.20]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681) [tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.20.jar:5.3.20]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) [tomcat-embed-core-9.0.63.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at com.sinoyd.boot.common.filter.SqlInjectFilter.doFilter(SqlInjectFilter.java:62) [sinoyd-boot-starter-common-5.2.1-20250612.054956-3.jar:5.2.1-SNAPSHOT]
	at sun.reflect.GeneratedMethodAccessor350.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_462]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_462]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.cloud.context.scope.GenericScope$LockedScopedProxyFactoryBean.invoke(GenericScope.java:485) [spring-cloud-context-3.1.5.jar:3.1.5]
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.boot.common.filter.SqlInjectFilter$$EnhancerBySpringCGLIB$$2.doFilter(<generated>) [sinoyd-boot-starter-common-5.2.1-20250612.054956-3.jar:5.2.1-SNAPSHOT]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at com.sinoyd.frame.filter.JwtUserAuthFilter.doFilterInternal(JwtUserAuthFilter.java:77) [frame-arch-6.0.0boot-20250909.090039-4.jar:6.0.0boot-SNAPSHOT]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:112) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:82) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183) [spring-security-web-5.7.1.jar:5.7.1]
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.20.jar:5.3.20]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.20.jar:5.3.20]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.__invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:41002) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1743) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.63.jar:9.0.63]
	at java.lang.Thread.run(Thread.java:750) [na:1.8.0_462]

2025-09-10 10:46:28.840  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 3, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:46:28.841  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 3
2025-09-10 10:46:45.148  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 4, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:46:45.148  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 4, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:47:00.320 ERROR 14944 --- [pool-8-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : {
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:47:00.321 ERROR 14944 --- [pool-8-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:47:43.964 ERROR 14944 --- [pool-8-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: syntax error, pos 1, line 1, column 2```json
{
  "参数列表": [
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "跟踪率",
      "参数值": "1.00",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "采样体积",
      "参数值": "1078.9",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "标况体积",
      "参数值": "0993.0",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均动压",
      "参数值": "0033",
      "量纲": "Pa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均静压",
      "参数值": "000.03",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均烟温",
      "参数值": "021.3",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均流速",
      "参数值": "06.14",
      "量纲": "m/s"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "烟气流量",
      "参数值": "0083995",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "标干流量",
      "参数值": "0074908",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "累计采时",
      "参数值": "060m00s",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "大气压",
      "参数值": "100.50",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均全压",
      "参数值": "000.05",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "计 压",
      "参数值": "-02.40",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "计 温",
      "参数值": "026.7",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "烟道截面",
      "参数值": "003.8000",
      "量纲": "m2"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "采嘴直径",
      "参数值": "06.0",
      "量纲": "mm"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "含湿量",
      "参数值": "03.10",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "皮托管系数",
      "参数值": "0.84",
      "量纲": ""
    }
  ]
}
```
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1510) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1390) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:181) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:47:43.965 ERROR 14944 --- [pool-8-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:47:43.976  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 4, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:47:43.976  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 4
2025-09-10 10:50:10.007  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 5, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:50:10.007  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 5, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:50:22.288 ERROR 14944 --- [pool-9-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

java.lang.NullPointerException: null
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:247) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:50:22.288 ERROR 14944 --- [pool-9-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:317) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:50:22.294  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 5, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:50:22.294  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 5
2025-09-10 10:54:06.292  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 10:54:06.297  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-09-10 10:54:06.300  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 10:54:06.303  INFO 14944 --- [http-nio-8980-exec-10] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 0 JPA repository interfaces.
2025-09-10 10:54:29.031  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 6, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:54:29.034  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 6, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:54:41.043 ERROR 14944 --- [pool-10-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

java.lang.NullPointerException: null
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:247) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:54:41.044 ERROR 14944 --- [pool-10-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:54:41.130  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 6, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:54:41.130  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 6
2025-09-10 10:58:13.641  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 7, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:58:13.641  INFO 14944 --- [http-nio-8980-exec-6] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 7, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:58:28.530 ERROR 14944 --- [pool-11-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : {
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:58:28.531 ERROR 14944 --- [pool-11-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 10:58:35.583  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 7, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:58:35.583  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 7
2025-09-10 10:59:50.982  INFO 14944 --- [http-nio-8980-exec-3] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 8, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 10:59:50.982  INFO 14944 --- [http-nio-8980-exec-3] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 8, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:00:06.360 ERROR 14944 --- [pool-12-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : {
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:00:06.361 ERROR 14944 --- [pool-12-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:00:13.312  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 8, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:00:13.312  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 8
2025-09-10 11:01:30.937  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 9, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:01:30.938  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 9, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:01:44.796 ERROR 14944 --- [pool-13-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : {
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:01:44.796 ERROR 14944 --- [pool-13-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:01:44.848  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 9, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:01:44.848  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 9
2025-09-10 11:03:54.376  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: a, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:03:54.376  INFO 14944 --- [http-nio-8980-exec-5] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: a, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:04:10.166 ERROR 14944 --- [pool-14-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : {
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:04:10.167 ERROR 14944 --- [pool-14-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:04:15.276  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: a, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:04:15.276  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: a
2025-09-10 11:04:28.532  WARN 14944 --- [http-nio-8980-exec-1] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6285b13f (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 11:04:28.537  WARN 14944 --- [http-nio-8980-exec-1] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@19caec2b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 11:04:28.541  WARN 14944 --- [http-nio-8980-exec-1] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@26c453db (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 11:05:19.830  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: b, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:05:19.831  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: b, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:05:34.574 ERROR 14944 --- [pool-15-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : ,
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:05:34.575 ERROR 14944 --- [pool-15-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:05:41.630  INFO 14944 --- [http-nio-8980-exec-4] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: b, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:05:41.630  INFO 14944 --- [http-nio-8980-exec-4] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: b
2025-09-10 11:07:47.043  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: c, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:07:47.044  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: c, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:08:43.052 ERROR 14944 --- [pool-16-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: syntax error, pos 1, line 1, column 2```json
{
  "参数列表": [
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "跟踪率",
      "参数值": "1.00",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "采样体积",
      "参数值": "1078.9",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "标况体积",
      "参数值": "0993.0",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均动压",
      "参数值": "0033",
      "量纲": "Pa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均静压",
      "参数值": "000.03",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均烟温",
      "参数值": "021.30",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均流速",
      "参数值": "06.14",
      "量纲": "m/s"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "烟气流量",
      "参数值": "0083995",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "标干流量",
      "参数值": "0074908",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "累计采时",
      "参数值": "060m00s",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "大气压",
      "参数值": "100.50",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "平均全压",
      "参数值": "000.05",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "计 压",
      "参数值": "-02.40",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "计 温",
      "参数值": "026.7",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "烟道截面",
      "参数值": "003.8000",
      "量纲": "m2"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "采嘴直径",
      "参数值": "06.0",
      "量纲": "mm"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "含湿量",
      "参数值": "03.10",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "",
      "参数名称": "皮托管系数",
      "参数值": "0.84",
      "量纲": ""
    }
  ]
}
```
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1510) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1390) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:181) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:08:43.054 ERROR 14944 --- [pool-16-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:08:43.098  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: c, 用户ID: 59141356591b48e18e139aa54d9dd351
2025-09-10 11:08:43.099  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: c
2025-09-10 11:21:29.884  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: d, 用户ID: 
2025-09-10 11:21:29.885  INFO 14944 --- [http-nio-8980-exec-7] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: d, 用户ID: 
2025-09-10 11:21:44.527 ERROR 14944 --- [pool-17-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

java.lang.NullPointerException: null
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:247) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:21:44.528 ERROR 14944 --- [pool-17-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 11:22:11.109  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: d, 用户ID: 
2025-09-10 11:22:11.109  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: d
2025-09-10 13:02:29.872  WARN 14944 --- [http-nio-8980-exec-2] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@575fcc7c (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 13:02:29.877  WARN 14944 --- [http-nio-8980-exec-2] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@55d054ff (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 13:02:29.881  WARN 14944 --- [http-nio-8980-exec-2] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@e460534 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 13:02:29.885  WARN 14944 --- [http-nio-8980-exec-2] com.zaxxer.hikari.pool.PoolBase          : instrumentparse52 - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@275304ac (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-09-10 13:15:58.535  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: e, 用户ID: 
2025-09-10 13:15:58.536  INFO 14944 --- [http-nio-8980-exec-10] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: e, 用户ID: 
2025-09-10 13:17:02.592  INFO 14944 --- [http-nio-8980-exec-4] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: e, 用户ID: 
2025-09-10 13:17:02.592  INFO 14944 --- [http-nio-8980-exec-4] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: e
2025-09-10 13:22:18.709  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: f, 用户ID: 
2025-09-10 13:22:18.710  INFO 14944 --- [http-nio-8980-exec-1] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: f, 用户ID: 
2025-09-10 13:22:34.051 ERROR 14944 --- [pool-19-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: not close json text, token : ,
	at com.alibaba.fastjson.parser.DefaultJSONParser.close(DefaultJSONParser.java:1556) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:185) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:22:34.052 ERROR 14944 --- [pool-19-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:23:26.267 ERROR 14944 --- [pool-19-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: syntax error, pos 1, line 1, column 2```json
{
  "参数列表": [
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "保留时间",
      "参数值": "3.843",
      "量纲": "min"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "峰面积",
      "参数值": "7.157",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "峰面积%",
      "参数值": "0.652",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "峰高",
      "参数值": "0.822",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "峰高%",
      "参数值": "1.257",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "F",
      "参数名称": "样品量",
      "参数值": "0.185",
      "量纲": "mg/L"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "保留时间",
      "参数值": "6.263",
      "量纲": "min"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "峰面积",
      "参数值": "529.133",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "峰面积%",
      "参数值": "48.183",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "峰高",
      "参数值": "46.004",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "峰高%",
      "参数值": "70.339",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "CL",
      "参数名称": "样品量",
      "参数值": "25.664",
      "量纲": "mg/L"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "保留时间",
      "参数值": "13.977",
      "量纲": "min"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "峰面积",
      "参数值": "561.894",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "峰面积%",
      "参数值": "51.166",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "峰高",
      "参数值": "18.577",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "峰高%",
      "参数值": "28.404",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "S04",
      "参数名称": "样品量",
      "参数值": "37.156",
      "量纲": "mg/L"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "保留时间",
      "参数值": "n.d.",
      "量纲": "min"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "峰面积",
      "参数值": "n.d.",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "峰面积%",
      "参数值": "n.d.",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "峰高",
      "参数值": "n.d.",
      "量纲": ""
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "峰高%",
      "参数值": "n.d.",
      "量纲": "%"
    },
    {
      "仪器编号": "P3",
      "样品编号": "0603063",
      "分析因子": "NO3",
      "参数名称": "样品量",
      "参数值": "n.d.",
      "量纲": "mg/L"
    }
  ]
}
```
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1510) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1390) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:181) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:23:26.268 ERROR 14944 --- [pool-19-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:23:26.341  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: f, 用户ID: 
2025-09-10 13:23:26.341  INFO 14944 --- [http-nio-8980-exec-2] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: f
2025-09-10 13:24:27.881  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接建立 - 类型: AI仪器解析实时数据, 会话ID: 10, 用户ID: 
2025-09-10 13:24:27.881  INFO 14944 --- [http-nio-8980-exec-8] c.s.p.w.UniversalWebSocketServer         : WebSocket连接建立成功 - 类型: AI仪器解析实时数据, 会话ID: 10, 用户ID: 
2025-09-10 13:24:40.687 ERROR 14944 --- [pool-20-thread-2] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

java.lang.NullPointerException: null
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:247) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:24:40.688 ERROR 14944 --- [pool-20-thread-2] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:25:29.980 ERROR 14944 --- [pool-20-thread-1] c.s.p.s.i.AiParseDataHandleServiceImpl   : AI解析数据转换失败：

com.alibaba.fastjson.JSONException: syntax error, pos 1, line 1, column 2```json
{
  "参数列表": [
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "跟踪率",
      "参数值": "1.00",
      "量纲": ""
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "采样体积",
      "参数值": "1078.9",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "标况体积",
      "参数值": "0993.0",
      "量纲": "L"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "平均动压",
      "参数值": "0033",
      "量纲": "Pa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "平均静压",
      "参数值": "000.03",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "平均烟温",
      "参数值": "021.30",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "平均流速",
      "参数值": "06.14",
      "量纲": "m/s"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "烟气流量",
      "参数值": "0083995",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "标干流量",
      "参数值": "0074908",
      "量纲": "m3/h"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "累计采时",
      "参数值": "060",
      "量纲": "m00s"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "大气压",
      "参数值": "100.50",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "平均全压",
      "参数值": "000.05",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "计压",
      "参数值": "-02.40",
      "量纲": "kPa"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "计温",
      "参数值": "026.7",
      "量纲": "C"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "烟道截面",
      "参数值": "003.8000",
      "量纲": "m2"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "采嘴直径",
      "参数值": "06.0",
      "量纲": "mm"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "含湿量",
      "参数值": "03.10",
      "量纲": "x"
    },
    {
      "仪器编号": "1A13280100",
      "样品编号": "00000012",
      "分析因子": "滤筒（膜）号",
      "参数名称": "皮托管系数",
      "参数值": "0.84",
      "量纲": ""
    }
  ]
}
```
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1510) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.parser.DefaultJSONParser.parse(DefaultJSONParser.java:1390) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:181) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:191) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parse(JSON.java:147) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSON.parseObject(JSON.java:252) ~[fastjson-1.2.83.jar:na]
	at com.alibaba.fastjson.JSONObject.getJSONObject(JSONObject.java:142) ~[fastjson-1.2.83.jar:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:244) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) [classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) [spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) [spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) [spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:25:29.980 ERROR 14944 --- [pool-20-thread-1] c.s.p.s.impl.AiParseFileAppServiceImpl   : AI解析接口数据转换失败：

com.alibaba.fastjson.JSONException: AI解析数据转换失败，请确保AI输出数据格式正确!{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl.saveParseDataRedis(AiParseDataHandleServiceImpl.java:265) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$FastClassBySpringCGLIB$$1.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.20.jar:5.3.20]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:704) ~[spring-aop-5.3.20.jar:5.3.20]
	at com.sinoyd.parse.service.impl.AiParseDataHandleServiceImpl$$EnhancerBySpringCGLIB$$1.saveParseDataRedis(<generated>) ~[classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.sendOcrRequestMsg(AiParseFileAppServiceImpl.java:322) [classes/:na]
	at com.sinoyd.parse.service.impl.AiParseFileAppServiceImpl.lambda$null$3(AiParseFileAppServiceImpl.java:155) [classes/:na]
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640) ~[na:1.8.0_462]
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_462]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_462]
	at java.lang.Thread.run(Thread.java:750) ~[na:1.8.0_462]

2025-09-10 13:25:30.059  INFO 14944 --- [http-nio-8980-exec-9] c.s.p.s.i.WebSocketManagerServiceImpl    : WebSocket连接关闭 - 类型: AI仪器解析实时数据, 会话ID: 10, 用户ID: 
2025-09-10 13:25:30.059  INFO 14944 --- [http-nio-8980-exec-9] c.s.p.w.UniversalWebSocketServer         : WebSocket连接关闭 - 会话ID: 10
2025-09-10 14:57:43.871  WARN 14944 --- [Thread-16] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-10 14:57:43.970  WARN 14944 --- [Thread-23] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-09-10 14:57:44.007  WARN 14944 --- [Thread-23] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-09-10 14:57:44.022  WARN 14944 --- [Thread-16] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-09-10 14:57:44.268  INFO 14944 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 14:57:44.270  INFO 14944 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown initiated...
2025-09-10 14:57:44.274  INFO 14944 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown completed.
2025-09-10 14:57:51.795  WARN 19188 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[shared-redis.yaml] & group[SHARED_REDIS]
2025-09-10 14:57:51.805  WARN 19188 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse] & group[DEFAULT_GROUP]
2025-09-10 14:57:51.811  WARN 19188 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse.yaml] & group[DEFAULT_GROUP]
2025-09-10 14:57:51.818  WARN 19188 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse-dev.yaml] & group[DEFAULT_GROUP]
2025-09-10 14:57:51.819  INFO 19188 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-shared-redis.yaml,SHARED_REDIS'}]
2025-09-10 14:57:51.840  INFO 19188 --- [main] c.sinoyd.EnableEurekaServerApplication   : The following 2 profiles are active: "${PROCOVER:dev}", "dev"
2025-09-10 14:57:52.834  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 14:57:52.834  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 14:57:53.004  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 164 ms. Found 21 JPA repository interfaces.
2025-09-10 14:57:53.019  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 14:57:53.020  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 14:57:53.044  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiInstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.045  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiParseFileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.045  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AppFlowDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.046  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.046  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasTestRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.047  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ErrorLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.047  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.047  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.048  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileFlowRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.048  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.049  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileResultAlaisRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.050  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.InstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.050  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.OrgStreamRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.050  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseDocumentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.051  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.052  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.052  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.053  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.053  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamErrLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.053  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.054  INFO 19188 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 14:57:53.054  INFO 19188 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
2025-09-10 14:57:53.394  INFO 19188 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=bf49610d-670f-392d-8457-df37c8b6197a
2025-09-10 14:57:53.676  INFO 19188 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'cacheManagerConfig' of type [com.sinoyd.frame.base.configuration.CacheManagerConfig$$EnhancerBySpringCGLIB$$3222584] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-09-10 14:57:54.048  INFO 19188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8980 (http)
2025-09-10 14:57:54.057  INFO 19188 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-10 14:57:54.058  INFO 19188 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-09-10 14:57:54.166  INFO 19188 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-10 14:57:54.166  INFO 19188 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2306 ms
2025-09-10 14:57:54.516  INFO 19188 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Starting...
2025-09-10 14:57:55.252  INFO 19188 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Start completed.
2025-09-10 14:57:55.326  INFO 19188 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 14:57:55.376  INFO 19188 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-10 14:57:55.517  INFO 19188 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-10 14:57:55.611  INFO 19188 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 14:57:56.403  INFO 19188 --- [main] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 14:57:56.554  INFO 19188 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 14:57:56.562  INFO 19188 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 14:57:56.841  WARN 19188 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-10 14:57:57.190  INFO 19188 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-10 14:58:00.372  INFO 19188 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@ab5c335, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5769679b, org.springframework.security.web.context.SecurityContextPersistenceFilter@76140ca5, org.springframework.security.web.header.HeaderWriterFilter@414d5660, org.springframework.web.filter.CorsFilter@2ef89e43, org.springframework.security.web.authentication.logout.LogoutFilter@4563d421, com.sinoyd.frame.filter.JwtUserAuthFilter@7bd76b79, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1621264e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5392c0a7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5fdfa35d, org.springframework.security.web.session.SessionManagementFilter@b9750f0, org.springframework.security.web.access.ExceptionTranslationFilter@51b1732d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c2c1790]
2025-09-10 14:58:01.635  WARN 19188 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-10 14:58:01.756  INFO 19188 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8980 (http) with context path ''
2025-09-10 14:58:01.908  INFO 19188 --- [main] c.sinoyd.EnableEurekaServerApplication   : Started EnableEurekaServerApplication in 12.518 seconds (JVM running for 13.261)
2025-09-10 14:58:01.920  INFO 19188 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse-dev.yaml, group=DEFAULT_GROUP
2025-09-10 14:58:01.920  INFO 19188 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse, group=DEFAULT_GROUP
2025-09-10 14:58:01.921  INFO 19188 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=shared-redis.yaml, group=SHARED_REDIS
2025-09-10 14:58:01.921  INFO 19188 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse.yaml, group=DEFAULT_GROUP
2025-09-10 14:58:02.450  INFO 19188 --- [main] com.sinoyd.grpc.start.GrpcStartService   : Server started, listening on 8910
2025-09-10 16:01:21.350  WARN 19188 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-09-10 16:01:21.352  WARN 19188 --- [Thread-2] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-09-10 16:01:21.366  WARN 19188 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-09-10 16:01:21.367  WARN 19188 --- [Thread-8] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-09-10 16:01:21.504  INFO 19188 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 16:01:21.506  INFO 19188 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown initiated...
2025-09-10 16:01:21.514  INFO 19188 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Shutdown completed.
2025-09-10 16:01:38.552  WARN 1552 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[shared-redis.yaml] & group[SHARED_REDIS]
2025-09-10 16:01:38.560  WARN 1552 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse] & group[DEFAULT_GROUP]
2025-09-10 16:01:38.566  WARN 1552 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse.yaml] & group[DEFAULT_GROUP]
2025-09-10 16:01:38.572  WARN 1552 --- [main] c.a.c.n.c.NacosPropertySourceBuilder     : Ignore the empty nacos configuration and get it based on dataId[sinoyd-instrumentParse-dev.yaml] & group[DEFAULT_GROUP]
2025-09-10 16:01:38.573  INFO 1552 --- [main] b.c.PropertySourceBootstrapConfiguration : Located property source: [BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse-dev.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse.yaml,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-sinoyd-instrumentParse,DEFAULT_GROUP'}, BootstrapPropertySource {name='bootstrapProperties-shared-redis.yaml,SHARED_REDIS'}]
2025-09-10 16:01:38.592  INFO 1552 --- [main] c.sinoyd.EnableEurekaServerApplication   : The following 2 profiles are active: "${PROCOVER:dev}", "dev"
2025-09-10 16:01:39.464  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 16:01:39.465  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-09-10 16:01:39.624  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 152 ms. Found 21 JPA repository interfaces.
2025-09-10 16:01:39.635  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-09-10 16:01:39.636  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-10 16:01:39.659  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiInstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.660  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AiParseFileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.661  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.AppFlowDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.662  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.662  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.DatasTestRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.663  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ErrorLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.664  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.664  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.665  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileFlowRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.665  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.666  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.FileResultAlaisRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.666  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.InstrumentConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.667  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.OrgStreamRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.667  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseDocumentRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.668  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.ParseLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.669  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamAppRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.670  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.671  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamDataRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.671  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamErrLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.672  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamLogRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.672  INFO 1552 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.sinoyd.parse.repository.StreamParamConfigRepository. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-09-10 16:01:39.673  INFO 1552 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-09-10 16:01:40.028  INFO 1552 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=9f992f23-5360-3cb2-90e5-b1ac13b1ca2e
2025-09-10 16:01:40.626  INFO 1552 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8980 (http)
2025-09-10 16:01:40.636  INFO 1552 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-09-10 16:01:40.637  INFO 1552 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.63]
2025-09-10 16:01:40.743  INFO 1552 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-09-10 16:01:40.743  INFO 1552 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2134 ms
2025-09-10 16:01:41.080  INFO 1552 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Starting...
2025-09-10 16:01:41.483  INFO 1552 --- [main] com.zaxxer.hikari.HikariDataSource       : instrumentparse52 - Start completed.
2025-09-10 16:01:41.542  INFO 1552 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-09-10 16:01:41.584  INFO 1552 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.9.Final
2025-09-10 16:01:41.727  INFO 1552 --- [main] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-09-10 16:01:41.844  INFO 1552 --- [main] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: com.sinoyd.frame.base.configuration.CustomMySQLDialect
2025-09-10 16:01:43.019  INFO 1552 --- [main] org.hibernate.tuple.PojoInstantiator     : HHH000182: No default (no-argument) constructor for class: com.sinoyd.parse.dto.DtoStreamData (class must be instantiated by Interceptor)
2025-09-10 16:01:43.165  INFO 1552 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-09-10 16:01:43.173  INFO 1552 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-09-10 16:01:43.320  WARN 1552 --- [main] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-09-10 16:01:43.656  INFO 1552 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'sinoyd-auth' URL not provided. Will try picking an instance via load-balancing.
2025-09-10 16:01:46.959  INFO 1552 --- [main] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@47c019d7, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7861a433, org.springframework.security.web.context.SecurityContextPersistenceFilter@cbf9cc1, org.springframework.security.web.header.HeaderWriterFilter@72cf43fa, org.springframework.web.filter.CorsFilter@f2e28c7, org.springframework.security.web.authentication.logout.LogoutFilter@18be7f57, com.sinoyd.frame.filter.JwtUserAuthFilter@494e7290, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@17d5138, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@18f919ce, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@e87c4cf, org.springframework.security.web.session.SessionManagementFilter@14ca4b4d, org.springframework.security.web.access.ExceptionTranslationFilter@650929dc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@314e240c]
2025-09-10 16:01:48.238  WARN 1552 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-09-10 16:01:48.366  INFO 1552 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8980 (http) with context path ''
2025-09-10 16:01:48.507  INFO 1552 --- [main] c.sinoyd.EnableEurekaServerApplication   : Started EnableEurekaServerApplication in 12.497 seconds (JVM running for 13.228)
2025-09-10 16:01:48.518  INFO 1552 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse-dev.yaml, group=DEFAULT_GROUP
2025-09-10 16:01:48.519  INFO 1552 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse, group=DEFAULT_GROUP
2025-09-10 16:01:48.519  INFO 1552 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=shared-redis.yaml, group=SHARED_REDIS
2025-09-10 16:01:48.519  INFO 1552 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=sinoyd-instrumentParse.yaml, group=DEFAULT_GROUP
2025-09-10 16:01:48.990  INFO 1552 --- [main] com.sinoyd.grpc.start.GrpcStartService   : Server started, listening on 8910
