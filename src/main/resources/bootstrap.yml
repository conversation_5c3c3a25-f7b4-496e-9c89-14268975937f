spring:
  application:
    name: ${APP_CODE:sinoyd-instrumentParse}
  jackson:
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_SERVER_ADDR:**************:8848}
        ip: ${NACOS_SERVER_IP:*************} # 指定当前机器所在的ip地址，优先级最高，不写的话自动获取
        port: ${NACOS_SERVER_PORT:8980} # 指定当应用使用的端口，优先级最高，不写的话自动获取
        group: ${NACOS_GROUP:DEFAULT_GROUP} #服务分组名
        weight: ${NACOS_WEIGHT:1} # 负载均衡的权重
        namespace: ${NACOS_NAMESPACE:a27fea7b-e4d1-40c1-9c99-bb2ad9917383} #命名空间
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: ${NACOS_FILE_EXTENSION:yaml}
        group: ${NACOS_GROUP:DEFAULT_GROUP} #服务分组名
        namespace: ${spring.cloud.nacos.discovery.namespace} #命名空间
        # 共享配置引用
        shared-configs:
          - data-id: shared-redis.yaml
            group: SHARED_REDIS
            refresh: true
      username: ${NACOS_USER:sinoyd} # nacos的用户名
      password: ${NACOS_PWD:SinoydQazXC^&$%123}  #nacos的密码
    sentinel:
      transport:
        dashboard: ${SENTINEL_SERVER_ADDR:localhost:18080}
        port: ${SENTINEL_API_PORT:8719}
      datasource:
        flow:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            dataId: ${spring.application.name}-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
            namespace: ${spring.cloud.nacos.discovery.namespace} #命名空间
            username: ${spring.cloud.nacos.username} # nacos的用户名
            password: ${spring.cloud.nacos.password}  #nacos的密码
        degrade:
          nacos:
            server-addr: ${spring.cloud.nacos.discovery.server-addr}
            dataId: ${spring.application.name}-degrade-rules
            groupId: SENTINEL_GROUP
            rule-type: degrade
            namespace: ${spring.cloud.nacos.discovery.namespace} #命名空间
            username: ${spring.cloud.nacos.username} # nacos的用户名
            password: ${spring.cloud.nacos.password}  #nacos的密码
  main:
   allow-bean-definition-overriding: true # 允许覆盖实例名
auth:
  serviceId: sinoyd-auth #认证服务中心实例名
  user:
    token-header: Authorization
    server-validate-token: false #是否启用认证中心端校验用户token，默认为false
  client:
    token-header: client-token
    secret: 12345678
    isEnabled: ${SINOYD_AUTH_CLIENT_ENABLED:false} #是否启用微服务
sinoyd:
  frame:
    restType: ${SINOYD_FRAME_RESTTYPE:service} # client 是通过微服务的方式调用，service 通过正常接口方式调用
    serviceId: ${SINOYD_FRAME_SERVICEID:*************:12001} # 调用的应用或者配置ip端口，client 就配置 应用，service就配置ip及端口，内部的即可
    model: ${SINOYD_FRAME_MODEL:false} # true 是微服务的框架版本，false 是 云服务的框架版本