server:
  port: ${PORT:8980}
  grpcPort: ${GRPC_PORT:8910}
frame-boot:
  restRequestTimeout: ${REST_TIME_OUT:5000}
api:
  # rest接口添加统一前缀
  prefix: /api

# spring
spring:
  cache:
    type: redis
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PWD:sinoyd}
    timeout: 10000
    database: ${REDIS_DB0:0}
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

  datasource:
    # sql连接配置
    driver-class-name: dm.jdbc.driver.DmDriver
    url: jdbc:dm://${DB_HOST:**************}:${DB_PORT:5342}?schema=${DB_NAME:INSTRUMENTPARSE}
    username: ${DB_USER:root} #oracle #devuser
    password: ${DB_PWD:Sinoyd**..123} #manager1 #qweAsd#21 #123qwe!@#
    hikari:
      maximum-pool-size: ${MAXIMUN_POOL_SIZE:100} #连接池最大链接数
      minimum-idle: ${MINIMUN_IDLE:5} #最小空闲连接数
      idle-timeout: ${IDLE_TIMEOUT:18000} #空闲链接存活最大时间，默认600000（10分钟）
      auto-commit: ${AUTO_COMMIT:true} #此属性控制从属性返回链接的默认自动提交行为，默认true
      max-lifetime: ${MAX_LIFETIME:1800000} #0表示无生命周期，默认是1800000（30分钟）
      connection-test-query: select 1
      connection-timeout: ${CONNECTION_TIMEOUT:30000} # 数据库链接超时时间，默认30秒
      pool-name: ${POOL_NAME:instrumentparse52}

  jpa:
     database-platform: com.sinoyd.frame.base.configuration.CustomDMDialect # DMserver  严重注意！
     hibernate:
#       ddl-auto: update
       naming:
         implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
         physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
     show-sql: true
     properties:
       hibernate:
         session_factory:
           statement_inspector: com.sinoyd.frame.inspector.JpaInterceptor
         format_sql: true


fileProps:
  filePath: ${FILE_PATH:E:/sinoyd/fileupload}

#logging
logging:
  level:
    root: info
  file:
    path: ${LOG_PATH:d:/SpringBoot}
    name: ${LOG_NAME:SinoydInstrumentParse.log}

publish:
  channel: ${PUBLISH_CHANNEL:SinoydLimsFileParseExecute}
