package com.sinoyd;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.frame.base.configuration.JdkDateSupport;
import com.sinoyd.frame.base.configuration.JdkTimestampSupport;
import com.sinoyd.grpc.start.GrpcStartService;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.ApplicationContext;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.WebApplicationInitializer;


/**
 * spring cloud系统启动入口
 */
@SpringBootApplication(scanBasePackages="com.sinoyd")
@EnableJpaAuditing(auditorAwareRef = "principalContextUser")
@EnableAsync
public class EnableEurekaServerApplication extends SpringBootServletInitializer implements WebApplicationInitializer {

    public static void main(String[] args) throws Exception {
        ApplicationContext applicationContext = new SpringApplicationBuilder(EnableEurekaServerApplication.class).run(args);
        JdkTimestampSupport.enable(DateUtil.FULL);//启用json时间序列化
        JdkDateSupport.enable(DateUtil.FULL);//启用json时间序列化格式
        GrpcStartService grpcServer = applicationContext.getBean(GrpcStartService.class);
        grpcServer.startGrpc();
    }
}
