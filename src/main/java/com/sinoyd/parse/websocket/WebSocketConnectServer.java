package com.sinoyd.parse.websocket;

import com.sinoyd.parse.service.WebSocketManagerService;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket工具类
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/26
 **/
@Slf4j
public abstract class WebSocketConnectServer {

    /**
     * 获取WebSocket管理器服务
     *
     * @return WebSocket管理器服务
     */
    protected static WebSocketManagerService getWebSocketManagerService() {
        return UniversalWebSocketServer.getWebSocketManagerService();
    }

    /**
     * 关闭WebSocket连接
     *
     * @param sessionId 会话ID
     * @return 是否成功关闭
     */
    public static int closeConnection(String sessionId) {
        WebSocketManagerService service = getWebSocketManagerService();
        if (service != null) {
            return service.removeConnection(sessionId) ? 1 : 0;
        }
        return 0;
    }
}
