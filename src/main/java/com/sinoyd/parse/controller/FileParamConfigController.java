package com.sinoyd.parse.controller;

import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.FileParamConfigService;
import com.sinoyd.parse.criteria.FileParamConfigCriteria;
import com.sinoyd.parse.dto.DtoFileParamConfig;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;

import java.util.List;


/**
 * FileParamConfig服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Validated
 @RestController
 @RequestMapping("api/parse/fileParamConfig")
 public class FileParamConfigController extends BaseJpaController<DtoFileParamConfig, String,FileParamConfigService> {


    /**
     * 分页动态条件查询FileParamConfig
     * @param fileParamConfigCriteria 条件参数
     * @return RestResponse<List<FileParamConfig>>
     */
     @GetMapping
     public RestResponse<List<DtoFileParamConfig>> findByPage(FileParamConfigCriteria fileParamConfigCriteria) {
         PageBean<DtoFileParamConfig> pageBean = super.getPageBean();
         RestResponse<List<DtoFileParamConfig>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileParamConfigCriteria);
         restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileParamConfig
     * @param id 主键id
     * @return RestResponse<DtoFileParamConfig>
     */
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileParamConfig> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         DtoFileParamConfig fileParamConfig = service.findOne(id);
         restResponse.setData(fileParamConfig);
         restResponse.setRestStatus(StringUtils.isNull(fileParamConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileParamConfig
     * @param fileParamConfig 实体列表
     * @return RestResponse<DtoFileParamConfig>
     */
     @PostMapping
     public RestResponse<DtoFileParamConfig> create(@Validated @RequestBody DtoFileParamConfig fileParamConfig) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileParamConfig));
         return restResponse;
      }

     /**
     * 新增FileParamConfig
     * @param fileParamConfig 实体列表
     * @return RestResponse<DtoFileParamConfig>
     */
     @PutMapping
     public RestResponse<DtoFileParamConfig> update(@Validated @RequestBody DtoFileParamConfig fileParamConfig) {
         RestResponse<DtoFileParamConfig> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileParamConfig));
         return restResponse;
      }

    /**
     * "根据id批量删除FileParamConfig
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }