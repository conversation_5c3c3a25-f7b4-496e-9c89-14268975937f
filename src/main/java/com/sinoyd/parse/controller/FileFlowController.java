package com.sinoyd.parse.controller;

import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.FileFlowService;
import com.sinoyd.parse.criteria.FileFlowCriteria;
import com.sinoyd.parse.dto.DtoFileFlow;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;

import java.util.List;


/**
 * FileFlow服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Validated
 @RestController
 @RequestMapping("api/parse/fileFlow")
 public class FileFlowController extends BaseJpaController<DtoFileFlow, String,FileFlowService> {


    /**
     * 分页动态条件查询FileFlow
     * @param fileFlowCriteria 条件参数
     * @return RestResponse<List<FileFlow>>
     */
     @GetMapping
     public RestResponse<List<DtoFileFlow>> findByPage(FileFlowCriteria fileFlowCriteria) {
         PageBean<DtoFileFlow> pageBean = super.getPageBean();
         RestResponse<List<DtoFileFlow>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileFlowCriteria);
         restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileFlow
     * @param id 主键id
     * @return RestResponse<DtoFileFlow>
     */
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileFlow> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         DtoFileFlow fileFlow = service.findOne(id);
         restResponse.setData(fileFlow);
         restResponse.setRestStatus(StringUtils.isNull(fileFlow) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileFlow
     * @param fileFlow 实体列表
     * @return RestResponse<DtoFileFlow>
     */
     @PostMapping
     public RestResponse<DtoFileFlow> create(@Validated @RequestBody DtoFileFlow fileFlow) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileFlow));
         return restResponse;
      }

     /**
     * 新增FileFlow
     * @param fileFlow 实体列表
     * @return RestResponse<DtoFileFlow>
     */
     @PutMapping
     public RestResponse<DtoFileFlow> update(@Validated @RequestBody DtoFileFlow fileFlow) {
         RestResponse<DtoFileFlow> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileFlow));
         return restResponse;
      }

    /**
     * "根据id批量删除FileFlow
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }