package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.DatasCriteria;
import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.parse.service.DatasService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * Datas服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@RestController
@RequestMapping("api/parse/datas")
public class DatasController extends BaseJpaController<DtoDatas, String, DatasService> {


    /**
     * 分页动态条件查询Datas
     *
     * @param datasCriteria 条件参数
     * @return RestResponse<List < Datas>>
     */
    @GetMapping
    public RestResponse<List<DtoDatas>> findByPage(DatasCriteria datasCriteria) {
        PageBean<DtoDatas> pageBean = super.getPageBean();
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, datasCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询Datas
     *
     * @param id 主键id
     * @return RestResponse<DtoDatas>
     */
    @GetMapping(path = "/{id}")
    public RestResponse<DtoDatas> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        DtoDatas datas = service.findOne(id);
        restResponse.setData(datas);
        restResponse.setRestStatus(StringUtils.isNull(datas) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增Datas
     *
     * @param datas 实体列表
     * @return RestResponse<DtoDatas>
     */
    @PostMapping
    public RestResponse<DtoDatas> create(@RequestBody DtoDatas datas) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        restResponse.setData(service.save(datas));
        return restResponse;
    }

    /**
     * 新增Datas
     *
     * @param datas 实体列表
     * @return RestResponse<DtoDatas>
     */
    @PutMapping
    public RestResponse<DtoDatas> update(@RequestBody DtoDatas datas) {
        RestResponse<DtoDatas> restResponse = new RestResponse<>();
        restResponse.setData(service.update(datas));
        return restResponse;
    }

    /**
     * "根据id批量删除Datas
     *
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 根据id批量查询Datas
     *
     * @param ids id列表
     * @return 响应结果
     */
    @PostMapping("/ids")
    public RestResponse<List<DtoDatas>> findByIds(@RequestBody List<String> ids) {
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        restResponse.setData(service.findAll(ids));
        return restResponse;
    }

    @PostMapping("/sampleCodes")
    public RestResponse<List<DtoDatas>> findBySampleCodes(@RequestBody List<String> sampleCodes) {
        RestResponse<List<DtoDatas>> restResponse = new RestResponse<>();
        restResponse.setData(service.findBySampleCodes(sampleCodes));
        return restResponse;
    }
}