package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.AiInstrumentConfigCriteria;
import com.sinoyd.parse.dto.DtoAiInstrumentConfig;
import com.sinoyd.parse.service.AiInstrumentConfigService;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;

/**
 * AI仪器解析配置服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
@RestController
@RequestMapping("api/parse/aiInstrumentConfig")
public class AiInstrumentConfigController extends BaseJpaController<DtoAiInstrumentConfig, String, AiInstrumentConfigService> {

    /**
     * 分页动态条件查询AI仪器解析配置
     *
     * @param aiInstrumentConfigCriteria 条件参数
     * @return RestResponse<List<DtoAiInstrumentConfig>>
     */
    @GetMapping
    public RestResponse<List<DtoAiInstrumentConfig>> findByPage(AiInstrumentConfigCriteria aiInstrumentConfigCriteria) {
        PageBean<DtoAiInstrumentConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoAiInstrumentConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, aiInstrumentConfigCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 新增AI仪器解析配置
     *
     * @param aiInstrumentConfig AI仪器解析配置实体
     * @return 新增后的AI仪器解析配置实体
     */
    @PostMapping
    public RestResponse<DtoAiInstrumentConfig> save(@RequestBody DtoAiInstrumentConfig aiInstrumentConfig) {
        RestResponse<DtoAiInstrumentConfig> restResponse = new RestResponse<>();
        DtoAiInstrumentConfig result = service.save(aiInstrumentConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 修改AI仪器解析配置
     *
     * @param aiInstrumentConfig AI仪器解析配置实体
     * @return 更新后的AI仪器解析配置实体
     */
    @PutMapping
    public RestResponse<DtoAiInstrumentConfig> update(@RequestBody DtoAiInstrumentConfig aiInstrumentConfig) {
        RestResponse<DtoAiInstrumentConfig> restResponse = new RestResponse<>();
        DtoAiInstrumentConfig result = service.update(aiInstrumentConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据ID查询AI仪器解析配置
     *
     * @param id 配置ID
     * @return AI仪器解析配置详情
     */
    @GetMapping("/{id}")
    public RestResponse<DtoAiInstrumentConfig> findById(@PathVariable String id) {
        RestResponse<DtoAiInstrumentConfig> restResponse = new RestResponse<>();
        DtoAiInstrumentConfig result = service.findOne(id);
        restResponse.setRestStatus(result != null ? ERestStatus.SUCCESS : ERestStatus.UNMATCH_RECORD);
        restResponse.setData(result);
        return restResponse;
    }

    /**
     * 根据ID删除AI仪器解析配置
     *
     * @param ids 配置ID集合
     * @return 删除结果
     */
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
