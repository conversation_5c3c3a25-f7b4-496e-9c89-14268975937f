package com.sinoyd.parse.controller;

import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.service.DatasTestService;
import com.sinoyd.parse.criteria.DatasTestCriteria;
import com.sinoyd.parse.dto.DtoDatasTest;
import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.frame.base.util.PageBean;
import org.springframework.web.bind.annotation.*;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;

import java.util.List;


/**
 * DatasTest服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @RestController
 @RequestMapping("api/parse/datasTest")
 public class DatasTestController extends BaseJpaController<DtoDatasTest, String,DatasTestService> {


    /**
     * 分页动态条件查询DatasTest
     * @param datasTestCriteria 条件参数
     * @return RestResponse<List<DatasTest>>
     */
     @GetMapping
     public RestResponse<List<DtoDatasTest>> findByPage(DatasTestCriteria datasTestCriteria) {
         PageBean<DtoDatasTest> pageBean = super.getPageBean();
         RestResponse<List<DtoDatasTest>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, datasTestCriteria);
         restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询DatasTest
     * @param id 主键id
     * @return RestResponse<DtoDatasTest>
     */
     @GetMapping(path = "/{id}")
     public RestResponse<DtoDatasTest> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoDatasTest> restResponse = new RestResponse<>();
         DtoDatasTest datasTest = service.findOne(id);
         restResponse.setData(datasTest);
         restResponse.setRestStatus(StringUtils.isNull(datasTest) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增DatasTest
     * @param datasTest 实体列表
     * @return RestResponse<DtoDatasTest>
     */
     @PostMapping
     public RestResponse<DtoDatasTest> create(@RequestBody DtoDatasTest datasTest) {
         RestResponse<DtoDatasTest> restResponse = new RestResponse<>();
         restResponse.setData(service.save(datasTest));
         return restResponse;
      }

     /**
     * 新增DatasTest
     * @param datasTest 实体列表
     * @return RestResponse<DtoDatasTest>
     */
     @PutMapping
     public RestResponse<DtoDatasTest> update(@RequestBody DtoDatasTest datasTest) {
         RestResponse<DtoDatasTest> restResponse = new RestResponse<>();
         restResponse.setData(service.update(datasTest));
         return restResponse;
      }

    /**
     * "根据id批量删除DatasTest
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }