package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.ParseDocumentCriteria;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.parse.service.ParseDocumentService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 仪器解析文件管理服务接口定义
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@RestController
@RequestMapping("api/parse/parseDocument")
public class ParseDocumentController extends BaseJpaController<DtoParseDocument, String, ParseDocumentService> {

    /**
     * 分页动态条件查询仪器解析文件
     *
     * @param parseDocumentCriteria 条件参数
     * @return RestResponse<List<DtoParseDocument>>
     */
    @GetMapping
    public RestResponse<List<DtoParseDocument>> findByPage(ParseDocumentCriteria parseDocumentCriteria) {
        PageBean<DtoParseDocument> pageBean = super.getPageBean();
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, parseDocumentCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 上传文档
     *
     * @param request 请求对象
     * @return RestResponse<List<DtoParseDocument>>
     */
    @PostMapping("/upload")
    public RestResponse<List<DtoParseDocument>> fileUpload(HttpServletRequest request) {
        RestResponse<List<DtoParseDocument>> restResponse = new RestResponse<>();
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(service.uploadFile(request));
        return restResponse;
    }

    /**
     * 文件下载
     *
     * @param documentId 下载的Id
     * @param response   响应流
     * @return 返回数据
     */
    @GetMapping("/download/{documentId}")
    public RestResponse<Void> fileDownload(@PathVariable String documentId, HttpServletResponse response) {
        service.download(documentId, response);
        return new RestResponse<>();
    }

    /**
     * 文件预览
     *
     * @param vo       文件预览传输对象
     * @param response 响应流
     */
    @PostMapping("/preview")
    public void preview(@RequestBody DocumentPreviewVO vo,
                        HttpServletResponse response) {
        service.preview(vo, response);
    }

    /**
     * 提供统一接口获取相应的文件路径
     *
     * @param code 编号
     * @param map  map数据参数
     * @return 返回数据
     */
    @PostMapping("/{code}")
    public RestResponse<String> getDocumentPath(@PathVariable String code, @RequestBody Map<String, Object> map) throws Exception {
        RestResponse<String> restResp = new RestResponse<>();
        restResp.setData(service.getDocumentPath(code, map));
        return restResp;
    }

    /**
     * 删除附件
     *
     * @param ids 附件id集合
     * @return 删除的条数
     */
    @DeleteMapping
    public RestResponse<Integer> delete(@RequestBody Collection<String> ids) {
        RestResponse<Integer> restResponse = new RestResponse<>();
        restResponse.setData(service.logicDeleteById(ids));
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        return restResponse;
    }
}
