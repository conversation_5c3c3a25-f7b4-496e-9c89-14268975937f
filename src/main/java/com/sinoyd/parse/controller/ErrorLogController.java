package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.ErrorLogCriteria;
import com.sinoyd.parse.dto.DtoErrorLog;
import com.sinoyd.parse.service.ErrorLogService;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * ErrorLog服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @RestController
 @RequestMapping("api/parse/errorLog")
 public class ErrorLogController extends BaseJpaController<DtoErrorLog, String,ErrorLogService> {


    /**
     * 分页动态条件查询ErrorLog
     * @param errorLogCriteria 条件参数
     * @return RestResponse<List<ErrorLog>>
     */
     @GetMapping
     public RestResponse<List<DtoErrorLog>> findByPage(ErrorLogCriteria errorLogCriteria) {
         PageBean<DtoErrorLog> pageBean = super.getPageBean();
         RestResponse<List<DtoErrorLog>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, errorLogCriteria);
         restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按流程parselogId查询ErrorLog
     * @param parselogId 日志Id
     * @return RestResponse<List<DtoErrorLog>>
     */
     @GetMapping(path = "/{parselogId}")
     public RestResponse<DtoErrorLog> find(@PathVariable(name = "parselogId") String parselogId) {
         RestResponse<DtoErrorLog> restResponse = new RestResponse<>();
         DtoErrorLog errorLog = service.findErrorLog(parselogId);
         restResponse.setData(errorLog);
         restResponse.setRestStatus(StringUtils.isNull(errorLog) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增ErrorLog
     * @param errorLog 实体列表
     * @return RestResponse<DtoErrorLog>
     */
     @PostMapping
     public RestResponse<DtoErrorLog> create(@RequestBody DtoErrorLog errorLog) {
         RestResponse<DtoErrorLog> restResponse = new RestResponse<>();
         restResponse.setData(service.save(errorLog));
         return restResponse;
      }

     /**
     * 新增ErrorLog
     * @param errorLog 实体列表
     * @return RestResponse<DtoErrorLog>
     */
     @PutMapping
     public RestResponse<DtoErrorLog> update(@RequestBody DtoErrorLog errorLog) {
         RestResponse<DtoErrorLog> restResponse = new RestResponse<>();
         restResponse.setData(service.update(errorLog));
         return restResponse;
      }

    /**
     * "根据id批量删除ErrorLog
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }