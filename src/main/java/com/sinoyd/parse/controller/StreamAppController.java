package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.StreamAppCriteria;
import com.sinoyd.parse.dto.DtoStreamApp;
import com.sinoyd.parse.dto.DtoStreamAppInstrumentConfig;
import com.sinoyd.parse.dto.DtoStreamAppMobileSync;
import com.sinoyd.parse.service.StreamAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 解析流应用服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Validated
@RestController
@RequestMapping("api/parse/streamApp")
public class StreamAppController extends BaseJpaController<DtoStreamApp, String, StreamAppService> {

    /**
     * 分页动态条件查询解析流应用
     *
     * @param streamAppCriteria 条件参数
     * @return RestResponse<List < StreamApp>>
     */
    @GetMapping
    public RestResponse<List<DtoStreamApp>> findByPage(StreamAppCriteria streamAppCriteria) {
        PageBean<DtoStreamApp> pageBean = super.getPageBean();
        RestResponse<List<DtoStreamApp>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, streamAppCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键id查询解析流应用信息
     *
     * @param id 解析流应用主键id
     * @return RestResponse<DtoStreamApp>
     */
    @GetMapping(path = "/{id}")
    public RestResponse<DtoStreamAppInstrumentConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStreamAppInstrumentConfig> restResponse = new RestResponse<>();
        DtoStreamAppInstrumentConfig dtoStreamAppInstrumentConfig = service.findStreamApp(id);
        restResponse.setData(dtoStreamAppInstrumentConfig);
        restResponse.setRestStatus(StringUtils.isNull(dtoStreamAppInstrumentConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }

    /**
     * 新增解析流应用配置
     *
     * @param dtoStreamAppInstrumentConfig 解析流应用及仪器配置实体
     * @return RestResponse<DtoStreamApp>
     */
    @PostMapping
    public RestResponse<DtoStreamAppInstrumentConfig> create(@Validated @RequestBody DtoStreamAppInstrumentConfig dtoStreamAppInstrumentConfig) {
        RestResponse<DtoStreamAppInstrumentConfig> restResponse = new RestResponse<>();
        DtoStreamAppInstrumentConfig data = service.saveStreamApp(dtoStreamAppInstrumentConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 复制解析流应用配置信息
     *
     * @param ids 应用id列表
     * @return RestResponse<DtoStreamApp>
     */
    @PostMapping("/copy")
    public RestResponse<List<DtoStreamApp>> create(@RequestBody List<String> ids) {
        RestResponse<List<DtoStreamApp>> restResponse = new RestResponse<>();
        List<DtoStreamApp> data = service.copyStreamApp(ids);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(data.size());
        return restResponse;
    }

    /**
     * 修改解析流应用信息
     *
     * @param dtoStreamAppInstrumentConfig 方案应用信息实体
     * @return RestResponse<DtoStreamAppInstrumentConfig>
     */
    @PutMapping
    public RestResponse<DtoStreamAppInstrumentConfig> update(@Validated @RequestBody DtoStreamAppInstrumentConfig dtoStreamAppInstrumentConfig) {
        RestResponse<DtoStreamAppInstrumentConfig> restResponse = new RestResponse<>();
        DtoStreamAppInstrumentConfig data = service.updateStreamApp(dtoStreamAppInstrumentConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 根据id批量删除解析流应用
     *
     * @param ids 方案参数配置id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteByIds(ids);
        restResp.setCount(count);
        return restResp;
    }

    /**
     * 移动端同步解析流应用(包含仪器配置信息)
     *
     * @return RestResponse<List<DtoStreamAppMobileSync>>
     */
    @GetMapping(path = "/mobileSync")
    public RestResponse<List<DtoStreamAppMobileSync>> appMobileSync() {
        RestResponse<List<DtoStreamAppMobileSync>> restResponse = new RestResponse<>();
        List<DtoStreamAppMobileSync> dtoStreamAppMobileSyncList = service.findMobileStreamApp();
        restResponse.setData(dtoStreamAppMobileSyncList);
        restResponse.setRestStatus(StringUtils.isNull(dtoStreamAppMobileSyncList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setCount(dtoStreamAppMobileSyncList.size());
        return restResponse;
    }
}