package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.StreamDataCriteria;
import com.sinoyd.parse.criteria.StreamDataMobileCriteria;
import com.sinoyd.parse.dto.DtoMobileStreamData;
import com.sinoyd.parse.dto.DtoStreamData;
import com.sinoyd.parse.service.StreamDataService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 现场解析数据服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Validated
@RestController
@RequestMapping("api/parse/streamData")
public class StreamDataController extends BaseJpaController<DtoStreamData, String, StreamDataService> {

    /**
     * 分页动态条件查询现场解析数据
     *
     * @param streamDataCriteria 条件参数
     * @return RestResponse<List<DtoStreamData>>
     */
    @GetMapping
    public RestResponse<List<DtoStreamData>> findByPage(StreamDataCriteria streamDataCriteria) {
        PageBean<DtoStreamData> pageBean = super.getPageBean();
        RestResponse<List<DtoStreamData>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, streamDataCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 移动端实时查询现场解析数据
     *
     * @param streamDataMobileCriteria 条件参数
     * @return RestResponse<List<StreamLog>>
     */
    @GetMapping(path = "/mobileQuery")
    public RestResponse<List<DtoMobileStreamData>> findByPage(StreamDataMobileCriteria streamDataMobileCriteria) {
        RestResponse<List<DtoMobileStreamData>> restResponse = new RestResponse<>();
        List<DtoMobileStreamData> dtoMobileStreamDataList = service.findMobileStreamData(streamDataMobileCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(dtoMobileStreamDataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(dtoMobileStreamDataList);
        restResponse.setCount(StringUtils.isEmpty(dtoMobileStreamDataList) ? 0 : dtoMobileStreamDataList.size());
        return restResponse;
    }

//    /**
//     * 移动端实时查询现场解析数据(分页)
//     *
//     * @param streamDataMobileCriteria 条件参数
//     * @return RestResponse<List<StreamLog>>
//     */
//    @ApiOperation(value = "移动端实时查询现场解析数据", notes = "移动端实时查询现场解析数据")
//    @GetMapping(path = "/mobileQuery")
//    public RestResponse<List<DtoMobileStreamData>> findByPage(StreamDataMobileCriteria streamDataMobileCriteria) {
//        PageBean<DtoMobileStreamData> pageBean = super.getPageBean();
//        RestResponse<List<DtoMobileStreamData>> restResponse = new RestResponse<>();
//        List<DtoMobileStreamData> dtoMobileStreamDataList = service.findMobileStreamDataByPage(pageBean, streamDataMobileCriteria);
//        restResponse.setRestStatus(StringUtils.isEmpty(dtoMobileStreamDataList) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
//        restResponse.setData(dtoMobileStreamDataList);
//        restResponse.setCount(StringUtils.isEmpty(dtoMobileStreamDataList) ? 0 : dtoMobileStreamDataList.size());
//        return restResponse;
//    }
    
    
}