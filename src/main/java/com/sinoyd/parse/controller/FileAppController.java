package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.FileAppCriteria;
import com.sinoyd.parse.dto.DtoFileApp;
import com.sinoyd.parse.service.FileAppService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * FileApp服务接口定义
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Validated
 @RestController
 @RequestMapping("api/parse/fileApp")
 public class FileAppController extends BaseJpaController<DtoFileApp, String,FileAppService> {


    /**
     * 分页动态条件查询FileApp
     * @param fileAppCriteria 条件参数
     * @return RestResponse<List<FileApp>>
     */
     @GetMapping
     public RestResponse<List<DtoFileApp>> findByPage(FileAppCriteria fileAppCriteria) {
         PageBean<DtoFileApp> pageBean = super.getPageBean();
         RestResponse<List<DtoFileApp>> restResponse = new RestResponse<>();
         service.findByPage(pageBean, fileAppCriteria);
         restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         restResponse.setData(pageBean.getData());
         restResponse.setCount(pageBean.getRowsCount());
         return restResponse;
     }

     /**
     * 按主键查询FileApp
     * @param id 主键id
     * @return RestResponse<DtoFileApp>
     */
     @GetMapping(path = "/{id}")
     public RestResponse<DtoFileApp> find(@PathVariable(name = "id") String id) {
         RestResponse<DtoFileApp> restResponse = new RestResponse<>();
         DtoFileApp fileApp = service.findOne(id);
         restResponse.setData(fileApp);
         restResponse.setRestStatus(StringUtils.isNull(fileApp) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
         return restResponse;
      }

    /**
     * 新增FileApp
     * @param fileApp 实体列表
     * @return RestResponse<DtoFileApp>
     */
     @PostMapping
     public RestResponse<DtoFileApp> create(@Validated @RequestBody DtoFileApp fileApp) {
         RestResponse<DtoFileApp> restResponse = new RestResponse<>();
         restResponse.setData(service.save(fileApp));
         return restResponse;
      }

      /**
       * 上传文件
       * @param appId 应用id
       * @param request 携带文件的请求
       * @return RestResponse<DtoFileApp>
       */
      @PostMapping("/file/{appId}")
      public RestResponse<String> upload(@PathVariable(name = "appId") String appId,
                                         HttpServletRequest request) {
          RestResponse<String> restResponse = new RestResponse<>();
          Boolean result = service.upload(appId, request);
          restResponse.setMsg(result ? ERestStatus.SUCCESS.getMsg() : "上传失败");
          return restResponse;
      }

    /**
     * 执行
     * @param ids id列表
     * @return RestResponse<DtoFileApp>
     */
    @PostMapping("/parse")
    public RestResponse<String> parse(@RequestBody List<String> ids) {
        RestResponse<String> restResponse = new RestResponse<>();
        Boolean result = service.parse(ids);
        restResponse.setMsg(result ? ERestStatus.SUCCESS.getMsg() : "执行失败");
        return restResponse;
    }

     /**
     * 新增FileApp
     * @param fileApp 实体列表
     * @return RestResponse<DtoFileApp>
     */
     @PutMapping
     public RestResponse<DtoFileApp> update(@Validated @RequestBody DtoFileApp fileApp) {
         RestResponse<DtoFileApp> restResponse = new RestResponse<>();
         restResponse.setData(service.update(fileApp));
         return restResponse;
      }

    /**
     * "根据id批量删除FileApp
     * @param ids id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.logicDeleteById(ids);
        restResp.setCount(count);
        return restResp;
    }
 }