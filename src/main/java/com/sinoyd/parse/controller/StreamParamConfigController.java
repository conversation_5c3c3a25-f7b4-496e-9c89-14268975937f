package com.sinoyd.parse.controller;

import com.sinoyd.boot.common.dto.RestResponse;
import com.sinoyd.boot.common.enums.ERestStatus;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.controller.BaseJpaController;
import com.sinoyd.parse.criteria.StreamParamConfigCriteria;
import com.sinoyd.parse.dto.DtoStreamParamConfig;
import com.sinoyd.parse.service.StreamParamConfigService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 解析流方案参数配置服务接口定义
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Validated
@RestController
@RequestMapping("api/parse/streamParamConfig")
public class StreamParamConfigController extends BaseJpaController<DtoStreamParamConfig, String, StreamParamConfigService> {

    /**
     * 分页动态条件查询解析流方案参数配置
     *
     * @param streamParamConfigCriteria 条件参数
     * @return RestResponse<List<StreamParamConfig>>
     */
    @GetMapping
    public RestResponse<List<DtoStreamParamConfig>> findByPage(StreamParamConfigCriteria streamParamConfigCriteria) {
        PageBean<DtoStreamParamConfig> pageBean = super.getPageBean();
        RestResponse<List<DtoStreamParamConfig>> restResponse = new RestResponse<>();
        service.findByPage(pageBean, streamParamConfigCriteria);
        restResponse.setRestStatus(StringUtils.isEmpty(pageBean.getData()) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        restResponse.setData(pageBean.getData());
        restResponse.setCount(pageBean.getRowsCount());
        return restResponse;
    }

    /**
     * 按主键查询解析流方案参数配置
     *
     * @param id 主键id
     * @return RestResponse<DtoStreamParamConfig>
     */
    @GetMapping(path = "/{id}")
    public RestResponse<DtoStreamParamConfig> find(@PathVariable(name = "id") String id) {
        RestResponse<DtoStreamParamConfig> restResponse = new RestResponse<>();
        DtoStreamParamConfig streamParamConfig = service.findStreamParamConfig(id);
        restResponse.setData(streamParamConfig);
        restResponse.setRestStatus(StringUtils.isNull(streamParamConfig) ? ERestStatus.UNMATCH_RECORD : ERestStatus.SUCCESS);
        return restResponse;
    }



    /**
     * 新增解析流方案参数配置信息
     *
     * @param streamParamConfig  方案参数配置实体
     * @return RestResponse<DtoStreamParamConfig>
     */
    @PostMapping
    public RestResponse<DtoStreamParamConfig> create(@Validated @RequestBody DtoStreamParamConfig streamParamConfig) {
        RestResponse<DtoStreamParamConfig> restResponse = new RestResponse<>();
        DtoStreamParamConfig data = service.saveStreamParamConfig(streamParamConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 修改解析流方案参数配置
     *
     * @param streamParamConfig 方案参数配置实体
     * @return RestResponse<DtoStreamParamConfig>
     */
    @PutMapping
    public RestResponse<DtoStreamParamConfig> update(@Validated @RequestBody DtoStreamParamConfig streamParamConfig) {
        RestResponse<DtoStreamParamConfig> restResponse = new RestResponse<>();
        DtoStreamParamConfig data = service.updateStreamParamConfig(streamParamConfig);
        restResponse.setRestStatus(ERestStatus.SUCCESS);
        restResponse.setData(data);
        restResponse.setCount(1);
        return restResponse;
    }

    /**
     * 根据id批量删除解析流方案参数配置
     *
     * @param ids 方案参数配置id列表
     * @return RestResponse<String>
     */
    @DeleteMapping
    public RestResponse<String> delete(@RequestBody List<String> ids) {
        RestResponse<String> restResp = new RestResponse<>();
        Integer count = service.deleteByIds(ids);
        restResp.setCount(count);
        return restResp;
    }
}