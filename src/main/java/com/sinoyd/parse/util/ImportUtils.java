package com.sinoyd.parse.util;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.constant.CommonConstant;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.dto.DtoCode;
import com.sinoyd.frame.service.CodeService;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.parse.constants.ParseConstants;
import com.sinoyd.parse.vo.CascadingDropdownVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFDataValidationHelper;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 导入导出工具类
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Component
@Slf4j
public class ImportUtils {

    private CodeService codeService;

    private final String US_DATE_FORMAT = "EEE MMM dd HH:mm:ss z yyyy";

    /**
     * 获取属性值为null的字段
     *
     * @param object 需要获取空字段名的类
     * @param list   需要赋值的空字段名的集合
     */
    public void getValue(Object object, List<String> list) {
        Field[] field = object.getClass().getSuperclass().getDeclaredFields();
        if (field.length == 0) {
            field = object.getClass().getDeclaredFields();
        }
        for (Field item : field) {
            String name = item.getName();
            name = name.substring(0, 1).toUpperCase() + name.substring(1);
            if ("SerialVersionUID".equals(name)) {
                continue;
            }
            Method m;
            Object value;
            try {
                m = object.getClass().getMethod("get" + name);
                value = m.invoke(object);
                if (value == null || "".equals(value)) {
                    list.add(name);
                }
            } catch (Exception e) {
                throw new BaseException("获取类属性值出错");
            }
        }
    }

    /**
     * 获取类中包含所有父级类的字段
     *
     * @param clazz     需要获取字段的类型
     * @param fieldList 已添加的字段数据
     * @return 所有字段数据
     */
    private List<Field> getClassFields(Class<?> clazz, List<Field> fieldList) {
        if (clazz == null) {
            return fieldList;
        }
        fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
        fieldList = getClassFields(clazz.getSuperclass(), fieldList);
        return fieldList;
    }

    /**
     * 判断对象是否为空
     *
     * @param obj          对象
     * @param isSuperClass 是否判断所有父类字段
     * @param ignoreFields 需要忽略判断空值的字段
     * @return 是否为null
     * @throws Exception 错误
     */
    public Boolean checkObjectIsNull(Object obj, Boolean isSuperClass, String... ignoreFields) throws Exception {
        List<String> ignoreList = ignoreFields != null ? Arrays.asList(ignoreFields) : new ArrayList<>();
        boolean result = true;
        List<Field> field = isSuperClass ? getClassFields(obj.getClass(), new ArrayList<>())
                : Arrays.asList(obj.getClass().getDeclaredFields());
        for (Field item : field) {
            String name = item.getName();
            name = name.substring(0, 1).toUpperCase() + name.substring(1);
            if (ignoreList.contains(name)) {
                continue;
            }
            Method m;
            Object value;
            m = obj.getClass().getMethod("get" + name);
            value = m.invoke(obj);
            if (value != null) {
                result = false;
            }
        }
        return result;
    }

    /**
     * 判断对象是否为空
     *
     * @param obj 对象
     * @return 是否为null
     * @throws Exception 错误
     */
    public Boolean checkObjectIsNull(Object obj) throws Exception {
        return checkObjectIsNull(obj, true, "RowNum", "Id", "IsStandard", "Detail");
    }

    /**
     * 双Sheet页导出
     *
     * @param sheetNames  工作簿名称
     * @param fistClass   第一张工作簿实体类
     * @param secondClass 第二张工作簿实体类
     * @param list1       第一张工作簿赋值数据
     * @param list2       第二张工作簿赋值数据
     * @param <T>         泛型
     * @return 工作单
     */
    public <T, Y> Workbook getWorkBook(Map<String, String> sheetNames, Class<T> fistClass, Class<Y> secondClass, List<T> list1, List<Y> list2) {
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(getSheetMap(sheetNames.get(ParseConstants.ImportConstants.FIRST_SHEET_NAME), fistClass, list1));
        sheetsList.add(getSheetMap(sheetNames.get(ParseConstants.ImportConstants.SECOND_SHEET_NAME), secondClass, list2));
        //endregion

        // 执行方法
        return ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
    }

    /**
     * 单Sheet页导出
     *
     * @param sheetNames 工作簿名称
     * @param fistClass  第一张工作簿实体类
     * @param list1      第一张工作簿赋值数据
     * @param <T>        泛型
     * @return 工作单
     */
    public <T> Workbook getWorkBook(Map<String, String> sheetNames, Class<T> fistClass, List<T> list1) {
        List<Map<String, Object>> sheetsList = new ArrayList<>();
        sheetsList.add(getSheetMap(sheetNames.get(ParseConstants.ImportConstants.FIRST_SHEET_NAME), fistClass, list1));
        // 执行方法
        return ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
    }


    /**
     * 多Sheet页导出
     *
     * @param sheetDataMap 工作簿名称和对应的数据 Map
     * @param sheetClasses 工作簿名称和对应class数据
     * @return 工作单
     */
    public Workbook getWorkBook(Map<String, List<?>> sheetDataMap, Map<String, Class<?>> sheetClasses) {
        List<Map<String, Object>> sheetsList = new ArrayList<>();

        for (Map.Entry<String, List<?>> entry : sheetDataMap.entrySet()) {
            String sheetName = entry.getKey();
            List<?> sheetData = entry.getValue();
            Class<?> sheetClass = sheetClasses.get(sheetName);
            sheetsList.add(getSheetMap(sheetName, sheetClass, sheetData));
        }
        // 执行方法
        return ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
    }

    /**
     * 获取多Sheet导出的sheetMap
     *
     * @param sheetName   导出的sheet名称
     * @param exportClass 导出类型
     * @param exportList  导出的集合
     * @return Map<String, Object>
     */
    private Map<String, Object> getSheetMap(String sheetName, Class<?> exportClass, List<?> exportList) {
        // Sheet参数
        ExportParams params = new ExportParams();
        // 设置样式
        params.setStyle(ExcelStyle.class);
        // 设置sheet名称
        if (StringUtils.isNotEmpty(sheetName)) {
            params.setSheetName(sheetName);
        }
        // 创建sheet1使用得map
        Map<String, Object> sheetMap = new HashMap<>();
        // title的参数为ExportParams类型，目前仅仅在ExportParams中设置了sheetName
        sheetMap.put("title", params);
        // 模版导出对应得实体类型
        sheetMap.put("entity", exportClass);
        // sheet中要填充得数据
        sheetMap.put("data", exportList);
        // 返回Map
        return sheetMap;
    }

    /**
     * 获取导入表的关联导入数据
     *
     * @param file        文件流
     * @param extendClass 关联的实体
     * @param <T>         泛型
     * @return ExcelImportResult
     * @throws Exception 错误信息
     */
    public <T> ExcelImportResult<T> getExcelData(MultipartFile file, Class<T> extendClass, Integer titleRowsNum, Integer headRowsNum, Integer sheetIndex, Boolean isNeedVerify, IExcelVerifyHandler<T> handler) throws Exception {
        ImportParams params = new ImportParams();
        params.setTitleRows(titleRowsNum);
        params.setHeadRows(headRowsNum);
        params.setStartSheetIndex(sheetIndex);
        params.setNeedVerify(isNeedVerify);
        if (isNeedVerify) {
            params.setVerifyHandler(handler);
        }
        return ExcelImportUtil.importExcelMore(file.getInputStream(), extendClass, params);
    }


    /**
     * 转换为Code以后添加常量
     *
     * @param isImportStr 需要导入的常量集合
     * @param codeType    常量类型
     */
    public void createCodes(List<String> isImportStr, String codeType, List<String> dbExtendData) {
        //移除获取到的空值
        isImportStr.removeIf(Objects::isNull);
        List<String> needImportNames = isImportStr.stream().filter(p -> !dbExtendData.contains(p.trim())).collect(Collectors.toList());
        if (StringUtils.isNotEmpty(needImportNames)) {
            for (String codeName : needImportNames) {
                DtoCode dtoCode=new DtoCode();
                dtoCode.setDictCode(codeName);
                dtoCode.setDictName(codeName);
                dtoCode.setDictValue("1");
                dtoCode.setDictType(codeType);
                dtoCode.setParentId(CommonConstant.TREE_ROOT);
                dtoCode.setSortNum(1000);
                codeService.create(dtoCode);
            }
        }
    }

    /**
     * 获取常量导入常量
     *
     * @param file   文件流
     * @param tClass 类
     * @param <T>    泛型
     * @return 常量集合
     */
    public <T> List<T> getImportNames(MultipartFile file, Class<T> tClass) {
        List<T> importNames;
        try {
            importNames = getExcelData(file, tClass, 0, 1, 1, true, null).getList();
        } catch (Exception e) {
            throw new BaseException("获取关联数据时出错");
        }
        //数据库中所有的部门Id
        return importNames;
    }

    /**
     * 数字格式验证
     *
     * @param result    返回  结果
     * @param checkStr  需要校验的字段
     * @param resultStr 返回的字符
     */
    public void checkNumTwo(ExcelVerifyHandlerResult result, Object checkStr, String resultStr, StringBuilder builder) {
        if (checkStr == null) {
            return;
        }
        try {
            if (!Pattern.matches("^-?\\d+(\\.\\d+)?$", checkStr.toString())) {
                result.setSuccess(false);
                builder.append("；").append(resultStr).append("格式错误");
            }
        } catch (Exception e) {
            result.setSuccess(false);
            builder.append("；").append(resultStr).append("格式错误");
        }
    }

    /**
     * 数字格式验证
     *
     * @param result    返回结果
     * @param checkStr  需要校验的字段
     * @param resultStr 返回的字符
     */
    public void checkEmail(ExcelVerifyHandlerResult result, Object checkStr, String resultStr, StringBuilder builder) {
        String checkString = (String) checkStr;
        if (StringUtils.isEmpty(checkString)) {
            return;
        }
        try {
            if (!Pattern.matches("^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$", checkStr.toString())) {
                result.setSuccess(false);
                builder.append("；").append(resultStr).append("格式不正确");
            }
        } catch (Exception e) {
            result.setSuccess(false);
            builder.append("；").append(resultStr).append("格式不正确");
        }
    }


//    /**
//     * 日期格式验证
//     *
//     * @param result    返回结果
//     * @param checkStr  需要校验的字段
//     * @param resultStr 返回的字符
//     */
//    public void checkDateTwo(ExcelVerifyHandlerResult result, Object checkStr, String resultStr, StringBuilder builder) {
//        ImportConfig importConfig = SpringContextAware.getBean(ImportConfig.class);
//        List<String> allowDateFormat = getSplitList(importConfig.getAllowDateFormat());
//        if (checkStr != null) {
//            String checkString = (String) checkStr;
//            if (!verifyDateFormat(checkString, allowDateFormat)) {
//                result.setSuccess(false);
//                allowDateFormat.remove(US_DATE_FORMAT);
//                builder.append("；").append(resultStr).append("格式不正确，可输入格式：").append(String.join("，", allowDateFormat));
//            }
//        }
//    }


    /**
     * 校验日期是否满足规定格式
     *
     * @param dateStr         日期字符串
     * @param allowDateFormat 可接受的日期格式
     * @return 满足格式返回true，否则返回false
     */
    public Boolean verifyDateFormat(String dateStr, List<String> allowDateFormat) {
        allowDateFormat.add(US_DATE_FORMAT);
        for (String format : allowDateFormat) {
            Boolean isDate = isValidDate(dateStr, format);
            if (isDate) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断日期数据是否合法
     *
     * @param dateStr 日期字符串
     * @param pattern 日期格式
     * @return 满足格式返回true，否则返回false
     */
    public Boolean isValidDate(String dateStr, String pattern) {
        SimpleDateFormat sdf;
        if (US_DATE_FORMAT.equals(pattern)) {
            sdf = new SimpleDateFormat(US_DATE_FORMAT, Locale.US);
        } else {
            sdf = new SimpleDateFormat(pattern);
        }
        sdf.setLenient(false);
        try {
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

//    /**
//     * 判断多种格式日期转换
//     *
//     * @param checkString 需要转换的字符串
//     * @return 日期
//     */
//    public Date stringToDateAllFormat(String checkString) {
//        Date tempTime = null;
//        if (StringUtils.isNotEmpty(checkString)) {
//            ImportConfig importConfig = SpringContextAware.getBean(ImportConfig.class);
//            List<String> allowDateFormat = getSplitList(importConfig.getAllowDateFormat());
//            allowDateFormat.add(US_DATE_FORMAT);
//            for (String format : allowDateFormat) {
//                if (US_DATE_FORMAT.equals(format)) {
//                    SimpleDateFormat sdf = new SimpleDateFormat(US_DATE_FORMAT, Locale.US);
//                    try {
//                        tempTime = sdf.parse(checkString);
//                    } catch (Exception e) {
//                        throw new BaseException("时间转换失败，请检查日期格式是否合法！");
//                    }
//                } else {
//                    tempTime = DateUtil.stringToDate(checkString, format);
//                }
//                if (tempTime != null) {
//                    return tempTime;
//                }
//            }
//        }
//        return tempTime;
//    }

    /**
     * 非空校验
     *
     * @param result    返回结果
     * @param checkStr  需要校验的字段
     * @param resultStr 校验结果
     * @param builder   校验结果
     */
    public void checkIsNull(ExcelVerifyHandlerResult result, String checkStr, String resultStr, StringBuilder builder) {
        if (StringUtils.isEmpty(checkStr)) {
            result.setSuccess(false);
            builder.append("；").append(resultStr).append("不能为空");

        } else {
            if ("".equals(checkStr.trim()) || "null".equals(checkStr.trim())) {
                result.setSuccess(false);
                builder.append("；").append(resultStr).append("不能为空");
            }
        }
    }

    /**
     * 判断身份证格式
     *
     * @param result    返回结果
     * @param checkStr  需要校验的字段
     * @param resultStr 校验结果
     * @param builder   校验结果
     */
    public void checkIdNum(ExcelVerifyHandlerResult result, String checkStr, String resultStr, StringBuilder builder) {
        if (StringUtils.isEmpty(checkStr)) {
            return;
        }
        try {
            if (!isIDNumber(checkStr)) {
                result.setSuccess(false);
                builder.append("；").append(resultStr).append("格式不正确");
            }
        } catch (Exception e) {
            result.setSuccess(false);
            builder.append("；").append(resultStr).append("格式不正确");
        }
    }

    /**
     * 人员字符串转集合
     *
     * @param personStr 字符串
     * @return 集合
     */
    public List<String> personStrToList(String personStr) {
        List<String> strList = new ArrayList<>();
        if (StringUtils.isNotEmpty(personStr)) {
            if (personStr.contains(",") || personStr.contains("，")) {
                String personsStr = personStr.replace("，", ",");
                String[] abilityPersons = personsStr.split(",");
                Collections.addAll(strList, abilityPersons);
            } else {
                strList.add(personStr);
            }
        }
        return strList;
    }

    /**
     * 去除字符串首尾空格
     *
     * @param object 需要处理的实体
     * @throws Exception 异常
     */
    public void strToTrim(Object object) throws Exception {
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if ("class java.lang.String".equals(field.getGenericType().toString())) {
                Method method = clazz.getMethod("get" + getMethodName(field.getName()));
                String value = (String) method.invoke(object);
                if (StringUtils.isNotEmpty(value)) {
                    value = value.trim();
                    field.setAccessible(true);
                    field.set(object, value);
                }
            }
        }
    }

    /**
     * 把一个字符串的第一个字母大写
     *
     * @param fieldName 需要处理的字符
     * @return 处理后的字符
     */
    private static String getMethodName(String fieldName) {
        byte[] items = fieldName.getBytes();
        items[0] = (byte) ((char) items[0] - 'a' + 'A');
        return new String(items);
    }

    /**
     * 获取字符串分割集合
     *
     * @param str 字符串
     * @return 集合
     */
    public List<String> getSplitList(String str) {
        List<String> resultList = new ArrayList<>();
        if (StringUtils.isNotEmpty(str)) {
            String replace = str.replace("，", ",");
            resultList = Stream.of(replace.split(",")).distinct().collect(Collectors.toList());
        }
        return resultList;
    }

    /**
     * 判断身份证格式是否正确
     *
     * @param IDNumber 身份证
     * @return 格式是否正确
     */
    public static boolean isIDNumber(String IDNumber) {
        if (IDNumber == null || "".equals(IDNumber)) {
            return false;
        }
        // 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
        String regularExpression = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|" +
                "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
        boolean matches = IDNumber.matches(regularExpression);
        //判断第18位校验值
        if (matches) {
            if (IDNumber.length() == 18) {
                try {
                    char[] charArray = IDNumber.toCharArray();
                    //前十七位加权因子
                    int[] idCardWi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
                    //这是除以11后，可能产生的11位余数对应的验证码
                    String[] idCardY = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
                    int sum = 0;
                    for (int i = 0; i < idCardWi.length; i++) {
                        int current = Integer.parseInt(String.valueOf(charArray[i]));
                        int count = current * idCardWi[i];
                        sum += count;
                    }
                    char idCardLast = charArray[17];
                    int idCardMod = sum % 11;
                    if (idCardY[idCardMod].toUpperCase().equals(String.valueOf(idCardLast).toUpperCase())) {
                        return true;
                    } else {
                        return false;
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    return false;
                }
            }
        }
        return matches;
    }

    /**
     * 设置工作单下拉数据源
     *
     * @param workbook 工作簿对象
     * @param firstCol 开始列
     * @param lastCol  结束列
     * @param strings  下拉框数据源
     */
    public void selectList(Workbook workbook, int firstCol, int lastCol, String[] strings) {
        Sheet sheet = workbook.getSheetAt(0);
        //  生成下拉列表
        //  只对(x，x)单元格有效
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 65535, firstCol, lastCol);
        //  生成下拉框内容
        DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(strings);
        HSSFDataValidation dataValidation = new HSSFDataValidation(cellRangeAddressList, dvConstraint);
        //  对sheet页生效
        sheet.addValidationData(dataValidation);

    }

    /**
     * 设置工作单指定sheet下拉数据源
     *
     * @param workbook   工作簿对象
     * @param firstCol   开始列
     * @param lastCol    结束列
     * @param strings    下拉框数据源
     * @param sheetIndex sheet页索引从0开始
     */
    public void selectList(Workbook workbook, int firstCol, int lastCol, String[] strings, int sheetIndex) {
        Sheet sheet = workbook.getSheetAt(sheetIndex);
        //  生成下拉列表
        //  只对(x，x)单元格有效
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 65535, firstCol, lastCol);
        //  生成下拉框内容
        DVConstraint dvConstraint = DVConstraint.createExplicitListConstraint(strings);
        HSSFDataValidation dataValidation = new HSSFDataValidation(cellRangeAddressList, dvConstraint);
        //  对sheet页生效
        sheet.addValidationData(dataValidation);

    }

    /**
     * 下拉数据总长度超过255时的解决办法，总体思路为：创建一个隐藏页存储下拉数据并建立单元格对其引用
     *
     * @param workbook            工作簿对象
     * @param sheetName           存放下拉数据的sheet页名称
     * @param sheetIndexForHidden 存放下拉数据的sheet页索引
     * @param sheetIndexForApply  应用下拉的sheet页索引
     * @param firstCol            开始列
     * @param lastCol             结束列
     * @param data                下拉数据
     */
    public void selectListMoreThan255(Workbook workbook, String sheetName, int sheetIndexForHidden, int sheetIndexForApply, int firstCol, int lastCol, String[] data) {
        Sheet hidden = workbook.createSheet(sheetName);
        Cell cell;
        for (int i = 0, length = data.length; i < length; i++) {
            Row row = hidden.createRow(i);
            cell = row.createCell(0);
            cell.setCellValue(data[i]);
        }
        Name name = workbook.createName();
        name.setNameName(sheetName);
        name.setRefersToFormula(sheetName + "!$A$1:$A$" + data.length);
        DVConstraint dvConstraint = DVConstraint.createFormulaListConstraint(sheetName);
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 65535, firstCol, lastCol);
        DataValidation dataValidation = new HSSFDataValidation(cellRangeAddressList, dvConstraint);
        workbook.setSheetHidden(sheetIndexForHidden, true);
        workbook.getSheetAt(sheetIndexForApply).addValidationData(dataValidation);
    }


    /**
     * 多Sheet导出
     * sheet名为导出实体的@table的name属性
     *
     * @param exportDataSet 导出的类对象
     */
    public <T> Workbook multiSheetWorkbook(T exportDataSet) throws IllegalAccessException {
        // 多个sheet配置参数
        final List<Map<String, Object>> sheetsList = new ArrayList<>();
        Class<?> aClass = exportDataSet.getClass();
        // 获取到全部属性
        Field[] declaredFields = aClass.getDeclaredFields();
        // 循环属性，每一个属性对应一个实体
        for (Field field : declaredFields) {
            field.setAccessible(true);
            String sheetName = "";
            Map<String, Object> exportMap = new HashMap<>();
            // 获取list<?>中?的泛型或者Set<?>
            if (field.getType().isAssignableFrom(List.class) || field.getType().isAssignableFrom(Set.class)) {
                // 如果是List类型，得到其Generic的类型
                Type fc = field.getGenericType();
                if (fc instanceof ParameterizedType) {
                    ParameterizedType pt = (ParameterizedType) fc;
                    // 得到泛型里的class类型对象。
                    Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                    // 把泛型中的class对象塞进map中
                    exportMap.put("entity", genericClazz);
                    Annotation[] annotations = genericClazz.getDeclaredAnnotations();
                    for (Annotation annotation : annotations) {
                        if (annotation.annotationType() == javax.persistence.Table.class) {
                            javax.persistence.Table tableAnnotation = (javax.persistence.Table) annotation;
                            // 获取注解的 name 属性
                            sheetName = tableAnnotation.name();
                        }
                    }
                }
                // 这里面就是数据
                exportMap.put("data", field.get(exportDataSet));
            } else {
                exportMap.put("entity", field.getType());
                exportMap.put("data", new ArrayList<>(Collections.singletonList(field.get(exportDataSet))));
            }
            // 设置sheet名字
            final ExportParams exportParams = new ExportParams(null, sheetName);
            exportMap.put("title", exportParams);
            // 加入多sheet配置列表
            sheetsList.add(exportMap);
        }
        return ExcelExportUtil.exportExcel(sheetsList, ExcelType.HSSF);
    }

    /**
     * 创建级联下拉框
     *
     * @param dataSheet  数据页sheet
     * @param dropdownVO 级联下拉框VO
     */
    public void createCascadeDropDownBox(Sheet dataSheet, CascadingDropdownVO dropdownVO) {
        LinkedHashMap<String, List<String>> areaList = dropdownVO.getDropdownDataList();
        String hiddenSheetName = dropdownVO.getDropdownDataSheetName();
        List<Integer> columnIdxList = dropdownVO.getColumnIdxList();
        Workbook workbook = dataSheet.getWorkbook();
        int startRowIdx = dropdownVO.getStartRowIdx();
        //获取所有sheet页个数
        int sheetTotal = workbook.getNumberOfSheets();
        //处理下拉数据
        if (areaList != null && areaList.size() != 0) {
            //新建一个sheet页
            Sheet dropdownDataSheet = workbook.getSheet(hiddenSheetName);
            if (dropdownDataSheet == null) {
                dropdownDataSheet = workbook.createSheet(hiddenSheetName);
                sheetTotal++;
            }
            dropdownDataSheet.protectSheet("Sinoyd**..123");
            int mainStart = 2;
            // 获取数据起始行
            int startRowNum = dropdownDataSheet.getLastRowNum();
            Set<String> keySet = areaList.keySet();
            for (String key : keySet) {
                Row fRow = dropdownDataSheet.createRow(startRowNum++);
                fRow.createCell(0).setCellValue(key);
                List<String> sons = areaList.get(key);
                for (int i = 1; i <= sons.size(); i++) {
                    fRow.createCell(i).setCellValue(sons.get(i - 1));
                }
                // 添加名称管理器
                String range = getRange(startRowNum, sons.size());
                Name name = workbook.getName(key);
                if (Objects.isNull(name)) {
                    name = workbook.createName();
                    //key不可重复
                    name.setNameName(key);
                    String formula = hiddenSheetName + "!" + range;
                    name.setRefersToFormula(formula);
                }
            }
            //将数据字典sheet页隐藏掉
            workbook.setSheetHidden(sheetTotal - 1, true);

            // 设置父级下拉
            //获取新sheet页内容
            String mainFormula = hiddenSheetName + "!$A$" + mainStart + ":$A$" + keySet.size();

            for (int i = 0; i < columnIdxList.size(); i++) {
                Integer col = columnIdxList.get(i);
                if (i == 0) {
                    // 设置下拉列表值绑定到主sheet页具体哪个单元格起作用
                    dataSheet.addValidationData(setDataValidation(dropdownDataSheet, mainFormula, startRowIdx, col, col));
                } else {
                    Integer fatherCol = columnIdxList.get(i - 1);
                    // 设置子级下拉
                    // 当前列为子级下拉框的内容受父级哪一列的影响
                    String indirectFormula = "INDIRECT($" + decimalToTwentyHex(fatherCol + 1) + (startRowIdx + 1 - startRowIdx) + ")";
                    dataSheet.addValidationData(setDataValidation(dropdownDataSheet, indirectFormula, startRowIdx, col, col));
                }
            }
        }
    }

    /**
     * 返回类型 DataValidation
     *
     * @param dropdownSheet 存放下拉框数据的sheet
     * @param strFormula    formula
     * @param firstRow      起始行
     * @param firstCol      起始列
     * @param endCol        终止列
     * @return 返回类型 DataValidation
     */
    private static DataValidation setDataValidation(Sheet dropdownSheet, String strFormula, int firstRow, int firstCol, int endCol) {
        DataValidationHelper dvHelper = new HSSFDataValidationHelper((HSSFSheet) dropdownSheet);
        CellRangeAddressList regions = new CellRangeAddressList(firstRow, 65535, firstCol, endCol);
        DVConstraint constraint = DVConstraint.createFormulaListConstraint(strFormula);
        DataValidation dataValidation = dvHelper.createValidation(constraint, regions);
        dataValidation.setSuppressDropDownArrow(false);
        dataValidation.setShowErrorBox(true);
        return dataValidation;
    }

    /**
     * 计算formula
     *
     * @param rowId    第几行
     * @param colCount 一共多少列
     * @return 如果给入参 1,1,10. 表示从B1-K1。最终返回 $B$1:$K$1
     */
    private static String getRange(int rowId, int colCount) {
        char start = (char) ('A' + 1);
        if (colCount <= 25) {
            char end = (char) (start + colCount - 1);
            return "$" + start + "$" + rowId + ":$" + end + "$" + rowId;
        } else {
            char endPrefix = 'A';
            char endSuffix = 'A';
            // 26-51之间，包括边界（仅两次字母表计算）
            if ((colCount - 25) / 26 == 0 || colCount == 51) {
                // 边界值
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                }
            } else {// 51以上
                if ((colCount - 25) % 26 == 0) {
                    endSuffix = (char) ('A' + 25);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26 - 1);
                } else {
                    endSuffix = (char) ('A' + (colCount - 25) % 26 - 1);
                    endPrefix = (char) (endPrefix + (colCount - 25) / 26);
                }
            }
            return "$" + start + "$" + rowId + ":$" + endPrefix + endSuffix + "$" + rowId;
        }
    }

    /**
     * 处理列名用于公式，十进制转二十六进制
     *
     * @param columnIdx 列索引，从0开始
     * @return 二十六进制列名
     */
    private static String decimalToTwentyHex(int columnIdx) {
        StringBuilder result = new StringBuilder();
        while (columnIdx > 0) {
            int remainder = columnIdx % 26;
            //大写A的ASCII码值为65
            result.append((char) (remainder + 64));
            columnIdx = columnIdx / 26;
        }
        return result.reverse().toString();
    }

    /**
     * 判断字符串是否是数字格式
     *
     * @param value 字符串
     * @return 是否是数字格式
     */
    public static Boolean isNumeral(Object value) {
        if (StringUtils.isNotNull(value)) {
            if (value instanceof Number) {
                return true;
            }
            if (value instanceof String) {
                return stringIsNumeral((String)value);
            }
        }
        return false;
    }

    private static Boolean stringIsNumeral(String value) {
        if (!value.contains("e") && !value.contains("E")) {
            if (StringUtils.isNotNullAndEmpty(value) && !value.trim().equals("")) {
                if (value.startsWith("-") || value.startsWith("+")) {
                    if (value.length() == 1) {
                        return false;
                    }
                    value = value.substring(1);
                }
                if (value.length() <= 2 || !value.startsWith("0x") && !value.startsWith("0X")) {
                    Integer p = 0;
                    Integer s = 0;
                    Integer l = value.length();
                    char[] charArr = value.toCharArray();
                    for(Integer i = 0; i < l; i = i + 1) {
                        if (charArr[i] == '.') {
                            if (p > 0 || s > 0 || i + 1 == l) {
                                return false;
                            }
                            p = i;
                        } else if (charArr[i] != 'e' && charArr[i] != 'E') {
                            if (charArr[i] < '0' || charArr[i] > '9') {
                                return false;
                            }
                        } else {
                            if (i == 0 || s > 0 || i + 1 == l) {
                                return false;
                            }
                            s = i;
                        }
                    }
                    return true;
                } else {
                    char[] charArr = value.substring(2).toCharArray();
                    char[] var2 = charArr;
                    int var3 = charArr.length;
                    for(int var4 = 0; var4 < var3; ++var4) {
                        char c = var2[var4];
                        if ((c < '0' || c > '9') && (c < 'a' || c > 'f') && (c < 'A' || c > 'F')) {
                            return false;
                        }
                    }
                    return true;
                }
            } else {
                return false;
            }
        } else {
            try {
                (new BigDecimal(value)).toPlainString();
            } catch (Exception var8) {
                return false;
            }
            return true;
        }
    }


    @Autowired
    public void setCodeService(CodeService codeService) {
        this.codeService = codeService;
    }
}
