package com.sinoyd.parse.service;

import com.sinoyd.common.vo.DocumentPreviewVO;
import com.sinoyd.parse.dto.DtoParseDocument;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.vo.UploadParamsVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 仪器解析文件管理操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface ParseDocumentService extends IBaseJpaService<DtoParseDocument, String> {

    /**
     * 根据对象ID查询文档列表
     *
     * @param objectId 对象ID
     * @return 文档列表
     */
    List<DtoParseDocument> findByObjectId(String objectId);

    /**
     * 上传路径
     *
     * @param request 上传内容（请求头）
     * @return 内容
     */
    List<DtoParseDocument> uploadFile(HttpServletRequest request);

    /**
     * 上传路径
     *
     * @param params  上传参数
     * @param request 上传内容（请求头）
     * @return 内容
     */
    List<DtoParseDocument> uploadFile(UploadParamsVO params, HttpServletRequest request);

    /**
     * 下载文件
     *
     * @param documentId 文件id
     * @param response   响应流
     */
    void download(String documentId, HttpServletResponse response);

    /**
     * 得到文件上传的路径
     *
     * @param code 通过编码锁定相应的反射类
     * @param map  前端传相应的参数集合
     * @return 返回文件上传的路径
     */
    String getDocumentPath(String code, Map<String, Object> map);

    /**
     * 文件预览
     *
     * @param vo       文件预览参数传输对象
     * @param response 响应流
     */
    void preview(DocumentPreviewVO vo, HttpServletResponse response);
}
