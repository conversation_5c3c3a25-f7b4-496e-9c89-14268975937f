package com.sinoyd.parse.service;

import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoDocument;
import com.sinoyd.parse.dto.DtoStreamConfig;
import com.sinoyd.parse.dto.DtoStreamParamConfig;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 解析流方案配置操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
public interface StreamConfigService extends IBaseJpaService<DtoStreamConfig, String> {

    /**
     * 按照解析流方案主键id查询方案信息
     *
     * @param id 解析流方案主键id
     * @return DtoStreamConfig
     */
    DtoStreamConfig findStreamConfig(String id);

    /**
     * 按照解析流方案主键id查询方案参数配置信息列表
     *
     * @param planId 解析流方案主键id
     * @return List<DtoStreamParamConfig>
     */
    List<DtoStreamParamConfig> findParamConfigByPlanId(String planId);

    /**
     * 新增解析流方案
     *
     * @param entity 解析流方案信息实体对象
     * @return DtoStreamConfig
     */
    DtoStreamConfig saveStreamConfig(DtoStreamConfig entity);

    /**
     * 更新解析流方案信息
     *
     * @param entity 解析流方案信息实体对象
     * @return DtoStreamConfig
     */
    DtoStreamConfig updateStreamConfig(DtoStreamConfig entity);


    /**
     * 按照解析流方案id列表 删除解析流方案信息
     *
     * @param ids 解析流方案信息对象列表
     * @return 删除的记录数
     */
    Integer deleteByIds(List<String> ids);

    /**
     * 方案文件上传
     *
     * @param request  请求对象
     * @param configId 方案id
     * @return 文件名称
     */
    String upload(HttpServletRequest request, String configId);

    /**
     * 方案文件下载
     *
     * @param configId 方案id
     * @param response
     * @return 下载文件路径
     */
    String download(String configId, HttpServletResponse response);

    /**
     * 方案文件下载
     *
     * @param configId  方案id
     * @return List<DtoDocument>
     */
    List<DtoDocument> getAttachInfo(String configId);

    /**
     * 删除方案附件
     *
     * @param configId  方案id
     */
    void deleteAttachInfo(String configId);

}