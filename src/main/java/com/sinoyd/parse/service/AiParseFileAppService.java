package com.sinoyd.parse.service;

import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.frame.service.IBaseJpaService;
import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.parse.vo.AiParseBatchVO;
import com.sinoyd.parse.vo.AiParseParamDataVO;

import java.util.List;

/**
 * AI仪器解析应用操作接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
public interface AiParseFileAppService extends IBaseJpaService<DtoAiParseFileApp, String> {

    /**
     * 批量AI解析（WebSocket长连接）
     *
     * @param parseBatchVO 批量解析参数
     */
    void batchParse(AiParseBatchVO parseBatchVO);

    /**
     * 获取AI解析应用的解析数据
     *
     * @param appId AI解析应用id
     * @return AI解析数据
     */
    List<AiParseParamDataVO> findAppParseData(String appId);

    /**
     * 获取AI解析应用的解析数据
     *
     * @param appId AI解析应用id
     * @return AI解析数据
     */
    List<DtoAppFlowData> findAppFlowData(String appId);

    /**
     * 获取附件上传路径数据
     *
     * @param id 解析应用id
     * @return 解析应用数据
     */
    DtoAiParseFileApp findAttachPath(String id);

    /**
     * 保存AI解析数据
     *
     * @param appId Ai
     * @param parseParamsData AI解析数据
     * @return 保存的AI解析数据
     */
    List<DtoDatas> saveDataList(String appId, List<AiParseParamDataVO> parseParamsData);
}
