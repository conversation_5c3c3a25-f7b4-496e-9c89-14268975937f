package com.sinoyd.parse.service;

import com.alibaba.fastjson.JSONObject;
import com.sinoyd.parse.dto.DtoAiParseFileApp;
import com.sinoyd.parse.dto.DtoAppFlowData;
import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.parse.vo.AiParseParamDataVO;
import com.sinoyd.parse.vo.AiParseStepVO;

import java.util.Collection;
import java.util.List;

/**
 * AI解析数据处理服务接口
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/28
 **/
public interface AiParseDataHandleService {

    /**
     * 从Redis中根据Id获取AI解析应用的解析数据
     *
     * @param appId AI解析应用id
     * @return AI解析数据
     */
    List<AiParseParamDataVO> findAppParseDataRedisByAppId(String appId);

    /**
     * 从Redis中根据Id获取AI解析步骤日志数据
     *
     * @param appId AI解析应用id
     * @return AI解析步骤日志数据
     */
    List<DtoAppFlowData> findAppFlowDataRedisByAppId(String appId);

    /**
     * 批量保存Redis数据到数据库
     *
     * @param appIds AI解析应用id集合
     */
    void batchSaveRedisDB(Collection<String> appIds);

    /**
     * 保存AI解析日志到Redis
     *
     * @param parseApp AI解析应用
     */
    DtoLog saveLogRedis(DtoAiParseFileApp parseApp);

    /**
     * 保存AI解析步骤日志到Redis
     *
     * @param appId  AI解析应用id
     * @param logId  AI解析日志id
     * @param stepVO AI解析步骤数据
     */
    void saveStepLogRedis(String appId, String logId, AiParseStepVO stepVO);

    /**
     * 保存AI解析数据到Redis
     *
     * @param appId     AI解析应用id
     * @param logId     AI解析日志id
     * @param dataJsObj AI解析接口返回Data数据
     * @return 保存的解析数据
     */
    List<AiParseParamDataVO> saveParseDataRedis(String appId, String logId, JSONObject dataJsObj);

    /**
     * 删除Redis数据
     *
     * @param redisType Redis类型
     */
    void deleteRedisByKey(Collection<String> appIds, String redisType);

    /**
     * 清空AI解析过程相关Redis数据
     *
     * @param appIds AI解析应用id集合
     */
    void deleteRedis(Collection<String> appIds);
}
