package com.sinoyd.parse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI解析步骤枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/09/02
 **/
@Getter
@AllArgsConstructor
public enum EnumAIParseStep {

    /**
     * ocr解析
     */
    OCR_PARSE("ocr_parse", "ocr解析", "md_content"),

    /**
     * ocr精调
     */
    REFINE_OCR("refine_ocr", "ocr精调", ""),

    /**
     * ocr解析完成
     */
    OCR_FINISH("ocr_finish", "ocr解析完成", ""),

    /**
     * lims数据解析
     */
    LIMS_DATA_PARSE("lims_data_parse", "lims数据解析", ""),

    /**
     * AI解析完成
     */
    AI_PARSE_FINISH("ai_parse_finish", "AI解析完成", ""),

    /**
     * AI解析失败
     */
    AI_PARSE_FAIL("ai_parse_fail", "AI解析失败", "");

    /**
     * 步骤名称
     */
    private String name;

    /**
     * 中文名称
     */
    private String c_name;

    /**
     * 流程内容字段名称（为空则显示全部内容）
     */
    private String contentName;

    /**
     * 根据步骤名称获取枚举
     *
     * @param stepName 步骤名称
     * @return 枚举
     */
    public static EnumAIParseStep getEnumByStepName(String stepName) {
        for (EnumAIParseStep step : EnumAIParseStep.values()) {
            if (step.getName().equals(stepName)) {
                return step;
            }
        }
        throw new IllegalArgumentException("不支持的解析步骤: " + stepName);
    }

}
