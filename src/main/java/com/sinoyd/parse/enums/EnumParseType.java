package com.sinoyd.parse.enums;

import com.sinoyd.boot.common.exception.BaseException;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析类型枚举
 * <AUTHOR>
 * @version V1.0.0 2020/12/23
 * @since V100R001
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumParseType {
    /**
     * 解析类型枚举
     */
    FILE("1", "文件解析"),
    DATA("2", "数据流解析"),
    FILE_TEST("3", "文件解析调试"),
    AI("4", "AI仪器解析");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析类型名称
     */
    private String name;

    /**
     * 获取枚举映射数据
     *
     * @return Map<String, String>
     */
    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumParseType.values())
                .collect(Collectors.toMap(EnumParseType::getValue, EnumParseType::getName));
    }

    /**
     * 根据枚举值获取枚举
     *
     * @param value 枚举值
     * @return 枚举
     */
    public static EnumParseType getEnumByValue(String value) {
        for (EnumParseType parseType : EnumParseType.values()) {
            if (parseType.getValue().equals(value)) {
                return parseType;
            }
        }
        throw new BaseException("不支持的解析类型: " + value);
    }
}
