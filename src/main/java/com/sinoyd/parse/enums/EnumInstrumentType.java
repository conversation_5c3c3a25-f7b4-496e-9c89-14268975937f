package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪器类型枚举
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumInstrumentType {
    /**
     * 现场仪器
     */
    FIELD("1", "现场仪器"),

    /**
     * 实验室仪器
     */
    LABORATORY("2", "实验室仪器");
    
    /**
     * 枚举值
     */
    private String value;
    
    /**
     * 仪器类型名称
     */
    private String name;

    /**
     * 获取枚举映射数据
     *
     * @return Map<String, String>
     */
    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumInstrumentType.values())
                .collect(Collectors.toMap(EnumInstrumentType::getValue, EnumInstrumentType::getName));
    }
}
