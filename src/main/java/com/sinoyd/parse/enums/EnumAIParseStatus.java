package com.sinoyd.parse.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 解析日志状态枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/26
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@Getter
public enum EnumAIParseStatus {
    /**
     * 解析日志状态枚举
     */
    UN_PARS("1", "未解析"),
    SUCCESS("2", "解析成功"),
    FAIL("3", "解析失败");
    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析日志状态名称
     */
    private String name;

    public static Map<String, String> getMapData() {
        return Arrays.stream(EnumAIParseStatus.values())
                .collect(Collectors.toMap(EnumAIParseStatus::getValue, EnumAIParseStatus::getName));
    }
}
