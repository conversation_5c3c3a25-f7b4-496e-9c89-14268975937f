package com.sinoyd.parse.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * AI解析类型枚举
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/26
 **/
@Getter
@AllArgsConstructor
public enum EnumAIParseType {

    IMAGE_RECOGNITION("1", "图像识别"),

    TEXT_EXTRACTION("2", "文本提取");

    /**
     * 枚举值
     */
    private String value;
    /**
     * 解析类型名称
     */
    private String name;

    public static EnumAIParseType getEnumByValue(String value) {
        for (EnumAIParseType parseType : EnumAIParseType.values()) {
            if (parseType.getValue().equals(value)) {
                return parseType;
            }
        }
        throw new IllegalArgumentException("不支持的解析类型: " + value);
    }

}
