package com.sinoyd.parse.verify;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.parse.dto.customer.DtoImportFileResultAlais;
import com.sinoyd.parse.enums.EnumResultType;
import com.sinoyd.parse.util.ImportUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

;

/**
 * 实验室仪器解析-解析方案结果映射导入数据校验
 *
 * <AUTHOR>
 * @version V1.0.0 2024/12/09
 * @since V100R001
 */
@Component
@Data
public class ResultAlaisVerifyHandle implements IExcelVerifyHandler<DtoImportFileResultAlais> {

    private ImportUtils importUtils;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(DtoImportFileResultAlais resultAlais) {
        //导入参数处理
        try {
            //去除空行
            if (importUtils.checkObjectIsNull(resultAlais)) {
                return new ExcelVerifyHandlerResult(true);
            }
            //去除首尾空格
            importUtils.strToTrim(resultAlais);
        } catch (Exception e) {
            throw new BaseException(e.getMessage());
        }
        ExcelVerifyHandlerResult result = new ExcelVerifyHandlerResult(true);
        StringBuilder failStr = new StringBuilder("第" + (resultAlais.getRowNum() + 1) + "行数据校验错误");

        //非空字段判断
        importUtils.checkIsNull(result, resultAlais.getResultType(), "结果类型", failStr);
        importUtils.checkIsNull(result, resultAlais.getParamName(), "结果原始名称", failStr);
        importUtils.checkIsNull(result, resultAlais.getParamAlias(), "结果映射名称", failStr);

        //判断结果类型是否存在
        isExistResultType(result, failStr, resultAlais);
        String failString = failStr.toString().replaceFirst("；", ":");
        result.setMsg(failString);
        return result;
    }

    /**
     * 判断结果类型是否存在
     *
     * @param result      校验结果
     * @param failStr     校验错误信息
     * @param resultAlais 当前导入的数据
     */
    private void isExistResultType(ExcelVerifyHandlerResult result, StringBuilder failStr, DtoImportFileResultAlais resultAlais) {
        //判断结果类型是否存在
        if (StringUtils.isNotEmpty(resultAlais.getResultType())) {
            String resultType = resultAlais.getResultType();
            if (!EnumResultType.NAME.getValue().equals(resultType) && !EnumResultType.NAME.getName().equals(resultType)
                    && !EnumResultType.PARAM.getValue().equals(resultType) && !EnumResultType.PARAM.getName().equals(resultType)) {
                result.setSuccess(false);
                failStr.append(";结果类型").append(resultType).append("不存在");
            }
        }
    }

    @Autowired
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}
