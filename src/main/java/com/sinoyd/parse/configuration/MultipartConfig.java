package com.sinoyd.parse.configuration;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.unit.DataSize;

import javax.servlet.MultipartConfigElement;

/**
 * 文件上传大小配置
 * <AUTHOR>
 * @version V1.0.0 2023/4/26
 * @since V100R001
 */
@Configuration
@Data
public class MultipartConfig {

    /**
     * 文件大小的限制
     */
    @Value(value = "${fileProps.maxFileSize:1024000000}")
    private Long maxFileSize;


    /**
     * 文件请求总大小的限制
     */
    @Value(value = "${fileProps.maxRequestSize:1024000000}")
    private Long maxRequestSize;

    /**
     * 文件上传配置
     */
    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        //文件最大
        factory.setMaxFileSize(DataSize.ofBytes(maxFileSize));
        /// 设置总上传数据总大小
        factory.setMaxRequestSize(DataSize.ofBytes(maxRequestSize));
        return factory.createMultipartConfig();
    }
}
