package com.sinoyd.parse.vo;

import com.sinoyd.parse.enums.EnumAIParseType;
import lombok.Data;

/**
 * ocr请求VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/27
 */
@Data
public class OcrRequestVO {

    /**
     * ocr请求地址
     */
    private String url;

    /**
     * ocr请求提示词
     */
    private String prompt;

    /**
     * ocr请求文件路径
     */
    private String filePath;

    /**
     * ocr请求地址后缀
     */
    private String urlSuffix;

    /**
     * ocr请求表单参数
     */
    private String formDataParam;

    /**
     * 根据解析类型填充请求地址和表单参数
     *
     * @param parseType 解析类型
     */
    public void fillTypeUrl(String parseType) {
        EnumAIParseType parseTypeE = EnumAIParseType.getEnumByValue(parseType);
        //根据解析类型获取请求参数
        switch (parseTypeE) {
            case IMAGE_RECOGNITION:
                urlSuffix = "/2?stream=true";
                formDataParam = "image_file";
                break;
            case TEXT_EXTRACTION:
                urlSuffix = "/7?stream=true";
                formDataParam = "file";
                break;
            default:
                urlSuffix = "";
                formDataParam = "";
                break;
        }
    }

    public OcrRequestVO() {
    }

    public OcrRequestVO(String url, String prompt, String filePath) {
        this.url = url;
        this.prompt = prompt;
        this.filePath = filePath;
    }
}
