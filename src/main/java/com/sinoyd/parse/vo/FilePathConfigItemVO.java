package com.sinoyd.parse.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlElement;

/**
 * 文件路径配置项
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/13
 **/
@Data
public class FilePathConfigItemVO {

    /**
     * 编号
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 类名
     */
    private String className;

    /**
     * 方法
     */
    private String method;

    /**
     * 占位符（多个按顺序隔开）
     */
    private String placeholder;

    /**
     * 路径
     */
    private String path;

    @XmlElement(name = "code")
    public String getCode() {
        return code;
    }

    @XmlElement(name = "name")
    public String getName() {
        return name;
    }

    @XmlElement(name = "className")
    public String getClassName() {
        return className;
    }

    @XmlElement(name = "method")
    public String getMethod() {
        return method;
    }

    @XmlElement(name = "placeholder")
    public String getPlaceholder() {
        return placeholder;
    }

    @XmlElement(name = "path")
    public String getPath() {
        return path;
    }
}
