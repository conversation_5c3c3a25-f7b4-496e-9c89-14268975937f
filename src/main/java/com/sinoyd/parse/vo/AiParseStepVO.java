package com.sinoyd.parse.vo;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.parse.enums.EnumAIParseStep;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * AI解析步骤VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/27
 **/
@Slf4j
@Data
public class AiParseStepVO {

    /**
     * 步骤id
     */
    private String stepId;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * Ocr步骤是否全部完成
     */
    private Boolean isOcrFinish;

    /**
     * 是否总流程完成
     */
    private Boolean isFinish;

    /**
     * 是否异常
     */
    private Boolean isError = false;

    /**
     * 异常类型
     */
    private String errorType;

    /**
     * 对象id
     */
    private String objectId;

    /**
     * 消息
     */
    private Object msg;

    /**
     * 默认构造方法
     */
    public AiParseStepVO() {
    }


    /**
     * 构造方法
     */
    public AiParseStepVO(String stepId, String stepName, Boolean isFinish, String objectId, Object msg) {
        this();
        this.stepId = stepId;
        this.stepName = stepName;
        this.isFinish = isFinish;
        this.objectId = objectId;
        this.msg = msg;
    }

    /**
     * 构造方法
     *
     * @param parseObj AI解析返回的数据对象
     */
    public AiParseStepVO(JSONObject parseObj, String objectId) {
        this();
        this.stepId = parseObj.getString("step_id");
        this.stepName = parseObj.getString("step_name");
        this.isOcrFinish = parseObj.getBoolean("done");
        this.isFinish = false;
        this.objectId = objectId;
        try{
            if (this.isOcrFinish) {
                this.msg = parseObj.get("workflow_result");
            } else {
                EnumAIParseStep stepEnum = EnumAIParseStep.getEnumByStepName(this.stepName);
                if (StringUtils.isNotEmpty(stepEnum.getContentName())) {
                    this.msg = parseObj.getJSONObject("step_result").getJSONObject(this.stepName).getOrDefault(stepEnum.getContentName(), "");
                } else {
                    this.msg = parseObj.getJSONObject("step_result").get(this.stepName);
                }
            }
        }catch (JSONException e){
            log.error("AI解析接口数据转换失败：", e);
            throw new JSONException("AI解析接口数据转换失败,检查Ai解析结果是否为JSON格式!");
        }

    }
}
