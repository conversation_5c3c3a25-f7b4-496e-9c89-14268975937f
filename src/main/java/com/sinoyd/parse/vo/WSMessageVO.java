package com.sinoyd.parse.vo;

import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.parse.enums.EnumMessageType;
import com.sinoyd.parse.enums.EnumWebSocketType;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * WebSocket消息实体
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/01/26
 **/
@Slf4j
@Data
public class WSMessageVO {

    /**
     * 消息ID
     */
    private String sessionId;

    /**
     * 消息类型
     */
    private EnumMessageType messageType;

    /**
     * 消息类型名称
     */
    private String messageTypeName;

    /**
     * WebSocket连接类型
     */
    private EnumWebSocketType webSocketType;

    /**
     * WebSocket连接类型名称
     */
    private String webSocketTypeName;

    /**
     * 消息内容
     */
    private Object content;

    /**
     * 默认构造函数
     */
    public WSMessageVO() {
    }

    /**
     * 构造函数
     *
     * @param webSocketType WebSocket连接类型
     * @param messageType   消息类型
     * @param content       消息内容
     */
    public WSMessageVO(EnumWebSocketType webSocketType, EnumMessageType messageType, String content) {
        this();
        this.webSocketType = webSocketType;
        this.webSocketTypeName = webSocketType.getDescription();
        this.messageType = messageType;
        this.content = content;
    }

    /**
     * 构造函数
     *
     * @param sessionId     会话id
     * @param webSocketType WebSocket连接类型
     * @param messageType   消息类型
     * @param content       消息内容
     */
    public WSMessageVO(String sessionId, EnumWebSocketType webSocketType, EnumMessageType messageType, Object content) {
        this.sessionId = sessionId;
        this.webSocketType = webSocketType;
        this.webSocketTypeName = webSocketType.getDescription();
        this.messageType = messageType;
        this.messageTypeName = messageType.getDescription();
        this.content = content;
    }

    /**
     * 获取JSON字符串
     *
     * @return JSON字符串
     */
    public String parseJsonString() {
        String json = "";
        try {
            json = JsonUtil.toJson(this);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException("JSON转换失败!");
        }
        return json;
    }
}
