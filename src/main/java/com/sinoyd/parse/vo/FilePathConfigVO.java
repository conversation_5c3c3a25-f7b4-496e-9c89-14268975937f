package com.sinoyd.parse.vo;

import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

/**
 * 文件路径配置
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Data
@XmlRootElement(name = "configs")
public class FilePathConfigVO {

    /**
     * 配置列表
     */
    List<FilePathConfigItemVO> filePathConfigs;

    /**
     * 获取配置列表
     *
     * @return 配置列表
     */
    @XmlElement(name = "config")
    public List<FilePathConfigItemVO> getFilePathConfigs() {
        return filePathConfigs;
    }
}
