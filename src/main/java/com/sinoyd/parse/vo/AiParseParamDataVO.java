package com.sinoyd.parse.vo;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.parse.dto.DtoDatas;
import lombok.Data;

/**
 * AI解析参数数据VO
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/27
 **/
@Data
public class AiParseParamDataVO {

    /**
     * 主键id
     */
    private String id = UUIDHelper.newId();

    /**
     * AI解析应用Id
     */
    private String appId;

    /**
     * 解析日志Id
     */
    private String parseLogId;

    /**
     * 仪器编号
     */
    private String instrumentCode;

    /**
     * 采样编号/样品编号
     */
    private String gatherCode;

    /**
     * 分析项目
     */
    private String analyzeItem;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 保存值
     */
    private String saveValue;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数据解析时间
     */
    private String parseDateTime;

    /**
     * 默认构造方法
     */
    public AiParseParamDataVO() {
    }

    /**
     * 构造方法
     *
     * @param data 解析数据实体
     */
    public AiParseParamDataVO(DtoDatas data) {
        this();
        this.appId = data.getAppId();
        this.parseLogId = data.getParselogId();
        this.instrumentCode = data.getInstrumentCode();
        this.gatherCode = data.getSampleCode();
        this.analyzeItem = data.getAnalyzeItemName();
        this.paramName = data.getParamName();
        this.saveValue = data.getValue();
        this.parseDateTime = DateUtil.dateToString(data.getParseDataTime(), DateUtil.FULL);
        //预留值1存储量纲单位
        this.unit = data.getExtend1();
    }
}
