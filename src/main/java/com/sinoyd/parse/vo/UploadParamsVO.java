package com.sinoyd.parse.vo;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 附件上传传参（预留，后续所有传递参数在此VO中管理）
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Data
@Accessors(chain = true)
public class UploadParamsVO {

    /**
     * 是否启用附件时间戳
     */
    private Boolean isEnableTimestamp;

    /**
     * 获取默认参数
     *
     * @return 默认参数
     */
    public static UploadParamsVO defaultParams() {
        return new UploadParamsVO()
                // 是否启用附件时间戳 默认：启用
                .setIsEnableTimestamp(true);
    }
}
