package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.parse.enums.EnumParseType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * FileApp查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileAppCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 应用名称、仪器名称、仪器编号、方案名称
    */
    private String key;
    /**
     * 解析类型
     */
    private String parseType;

    /**
     * 是否过滤过滤解析类型（默认过滤掉解析类型为3的数据）
     */
    private String filterParseType;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.planId = b.id ");
        if (StringUtils.isNotEmpty(this.key)) {
            condition.append(" and (LOWER(appName) like :key or LOWER(instrumentName) like :key or LOWER(instrumentCode) like :key or LOWER(planName) like :key) ");
            values.put("key", "%" + this.key.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(parseType)) {
            condition.append(" and parseType = :parseType ");
            values.put("parseType", this.parseType);
        }
        if (StringUtils.isNotEmpty(filterParseType) && "1".equals(filterParseType)) {
            condition.append(" and parseType != :filterParseType ");
            values.put("filterParseType", EnumParseType.FILE_TEST.getValue());
        }
        return condition.toString();
    }
}