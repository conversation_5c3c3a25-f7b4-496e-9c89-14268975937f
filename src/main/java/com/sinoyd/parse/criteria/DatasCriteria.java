package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;



/**
 * Datas查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DatasCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fromTime;

    private String toTime;
    /**
     * 关键字:仪器名称/编号
     */
    private String key1;
    /**
     * 关键字:分析项目
     */
    private String key2;
    /**
     * 样品编号
     */
    private String sampleCode;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 参数名称/别名
     */
    private String paramName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.parselogId = b.id ");
        if (StringUtils.isNotEmpty(this.key1)) {
            condition.append(" and ( LOWER(a.instrumentName) like :key1 or LOWER(a.instrumentCode) like :key1) ");
            values.put("key1", "%" + this.key1.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.key2)) {
            condition.append(" and ( LOWER(a.analyzeItemName) like :key2) ");
            values.put("key2", "%" + this.key2.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.paramName)) {
            condition.append(" and ( LOWER(a.paramName) like :paramName or LOWER(a.paramAlias) like :paramName) ");
            values.put("paramName", "%" + this.paramName.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.sampleCode)) {
            condition.append(" and ( LOWER(a.sampleCode) like :sampleCode) ");
            values.put("sampleCode", "%" + this.sampleCode.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.fileName)) {
            condition.append(" and ( LOWER(b.fileOrgName) like :fileOrgName) ");
            values.put("fileOrgName", "%" + this.fileName.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.fromTime)) {
            Date date = DateUtil.stringToDate(this.fromTime, DateUtil.FULL);
            condition.append(" and a.parseDataTime >= :fromTime");
            values.put("fromTime", date);
        }
        if (StringUtils.isNotEmpty(this.toTime)) {
            Date date = DateUtil.stringToDate(this.toTime, DateUtil.FULL);
            condition.append(" and a.parseDataTime <= :toTime");
            values.put("toTime", date);
        }
        return condition.toString();
    }
}