package com.sinoyd.parse.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;



/**
 * FileConfig查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FileConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 处理方式
    */
    private String handleType;

    /**
     * 方案名称
     */
    private String planName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.handleType)) {
            condition.append(" and handleType = :handleType");
            values.put("handleType", this.handleType);
        }
        if (StringUtils.isNotEmpty(this.planName)) {
            condition.append(" and planName like :planName");
            values.put("planName", "%" + this.planName + "%");
        }
        return condition.toString();
    }
}