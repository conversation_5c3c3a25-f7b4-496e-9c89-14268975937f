package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;



/**
 * DatasTest查询条件
 * <AUTHOR>
 * @version V1.0.0 2019年4月15日
 * @since   V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DatasTestCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
    * 应用id
    */
    private String appId;

    private String fromTime;

    private String toTime;
    /**
     * 关键字:样品编号、分析项目、参数名称
     */
    private String key;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 采集编号
     */
    private String gatherCode;

    /**
     * 分析项目
     */
    private String analyzeItemName;

    /**
     * 参数名称
     */
    private String paramName;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        condition.append(" and a.parselogId = b.id");
        if (StringUtils.isNotEmpty(this.appId)) {
            condition.append(" and a.appId = :appId");
            values.put("appId", this.appId);
        }
        if (StringUtils.isNotEmpty(this.key)) {
            condition.append(" and ( LOWER(a.sampleCode) like :key or LOWER(a.analyzeItemName) like :key or LOWER(a.paramName) like :key) ");
            values.put("key", "%" + this.key.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.fileName)) {
            condition.append(" and ( LOWER(b.fileOrgName) like :fileOrgName) ");
            values.put("fileOrgName", "%" + this.fileName.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.fromTime)) {
            Date date = DateUtil.stringToDate(this.fromTime, DateUtil.FULL);
            condition.append(" and a.parseDataTime >= :fromTime");
            values.put("fromTime", date);
        }
        if (StringUtils.isNotEmpty(this.toTime)) {
            Date date = DateUtil.stringToDate(this.toTime, DateUtil.FULL);
            condition.append(" and a.parseDataTime <= :toTime");
            values.put("toTime", date);
        }
        if (StringUtils.isNotEmpty(this.gatherCode)) {
            condition.append(" and LOWER(a.sampleCode) like :gatherCode");
            values.put("gatherCode", "%" + this.gatherCode.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.analyzeItemName)) {
            condition.append(" and LOWER(a.analyzeItemName) like :analyzeItemName");
            values.put("analyzeItemName", "%" + this.analyzeItemName.toLowerCase() + "%");
        }
        if (StringUtils.isNotEmpty(this.paramName)) {
            condition.append(" and LOWER(a.paramName) like :paramName");
            values.put("paramName", "%" + this.paramName.toLowerCase() + "%");
        }
        return condition.toString();
    }
}