package com.sinoyd.parse.criteria;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 解析流方案配置查询条件
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StreamParamConfigCriteria extends BaseCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数名称
     */
    private String paramName;

    /**
     * 方案id
     */
    private String planId;

    @Override
    public String getCondition() {
        values.clear(); //清除条件数据
        StringBuilder condition = new StringBuilder();
        if (StringUtils.isNotEmpty(this.planId)) {
            condition.append(" and a.planId = :planId ");
            values.put("planId", this.planId);
        }
        if (StringUtils.isNotEmpty(this.paramName)) {
            condition.append(" and (paramName like :planName or paramAlias like :paramAlias) ");
            values.put("planName", "%" + this.paramName + "%");
            values.put("paramAlias", "%" + this.paramName + "%");
        }
        condition.append(" and a.isDeleted = 0 ");
        condition.append(" order by a.orderNum desc");
        return condition.toString();
    }
}