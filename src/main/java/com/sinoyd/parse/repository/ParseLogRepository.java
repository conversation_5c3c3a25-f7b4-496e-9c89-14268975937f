package com.sinoyd.parse.repository;

import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.frame.repository.IBaseJpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**
 * Log数据访问操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
public interface ParseLogRepository extends IBaseJpaRepository<DtoLog, String> {

    /**
     * 根据应用配置id删除日志
     * @param appIds 应用配置id
     */
    @Transactional
    @Modifying
    @Query("update DtoLog as a set a.isDeleted=1, a.modifyDate=:modifyDate where a.appId in:appIds")
    void deleteByAppIds(@Param("appIds") List<String> appIds, @Param("modifyDate") Date modifyDate);

    /**
     * 根据应用id查询解析日志
     * @param appId 应用id
     * @return 日志数据集合
     */
    List<DtoLog> findByAppId(String appId);
}