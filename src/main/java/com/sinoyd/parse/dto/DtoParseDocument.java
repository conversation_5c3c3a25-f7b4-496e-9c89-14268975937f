package com.sinoyd.parse.dto;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.parse.entity.ParseDocument;

import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

/**
 * 仪器解析文件管理DTO实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/13
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_PARSE_ParseDocument")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoParseDocument extends ParseDocument {

    private static final long serialVersionUID = 1L;


    /**
     * 无参构造方法
     */
    public DtoParseDocument() {
        super();
    }

    /**
     * 上传文件
     *
     * @param objectId     对象id
     * @param folderName   文件夹名称
     * @param filename     文件名称
     * @param physicalName 物理文件名称
     * @param path         文件路径
     * @param docTypeId    文件类型
     * @param docTypeName  文件类型名称
     * @param docSize      文件大小
     * @param docSuffix    文件后缀
     */
    public DtoParseDocument(String objectId, String folderName, String filename, String physicalName, String path,
                            String docTypeId, String docTypeName, Integer docSize, String docSuffix) {
        this();
        setObjectId(objectId);
        setFolderName(folderName);
        setFilename(filename);
        setPhysicalName(physicalName);
        setPath(path);
        setDocTypeId(docTypeId);
        setDocTypeName(docTypeName);
        setDocSize(docSize);
        setDocSuffix(docSuffix);
        setOrderNum(0);
        setRemark("");
        setUploadPerson(PrincipalContextUser.getPrincipal().getUserName());
        setUploadPersonId(PrincipalContextUser.getPrincipal().getUserId());
        setOrgId(PrincipalContextUser.getPrincipal().getOrgId());
    }
}
