package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.StreamLog;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.util.Date;


/**
 * 解析流日志dto类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_StreamLog")
@Data
@DynamicInsert
public class DtoStreamLog extends StreamLog {
    private static final long serialVersionUID = 1L;

    @Transient
    private String instrumentName;

    @Transient
    private String instrumentCode;

    /**
     * 默认构造
     */
    public DtoStreamLog(){


    }


    public DtoStreamLog(String id, String serialNumber, String instrumentName, String instrumentCode, Date getTime, Date parseTime, String parseStatus) {
        setId(id);
        setSerialNumber(serialNumber);
        setInstrumentName(instrumentName);
        setInstrumentCode(instrumentCode);
        setGetTime(getTime);
        setParseTime(parseTime);
        setParseStatus(parseStatus);

    }

}