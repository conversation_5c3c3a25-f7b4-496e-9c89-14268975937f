package com.sinoyd.parse.dto;

import com.sinoyd.parse.entity.AiInstrumentConfig;
import javax.persistence.*;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.Where;

/**
 * AI仪器解析配置DTO实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/12
 **/
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "TB_Parse_AiInstrumentConfig")
@Where(clause = "isDeleted = 0")
@Data
@DynamicInsert
public class DtoAiInstrumentConfig extends AiInstrumentConfig {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 仪器类型名称
     */
    @Transient
    private String instrumentTypeName;
}
