package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.criteria.StreamParamConfigCriteria;
import com.sinoyd.parse.dto.DtoStreamConfig;
import com.sinoyd.parse.dto.DtoStreamParamConfig;
import com.sinoyd.parse.repository.StreamConfigRepository;
import com.sinoyd.parse.repository.StreamParamConfigRepository;
import com.sinoyd.parse.service.StreamConfigService;
import com.sinoyd.parse.service.StreamParamConfigService;
import com.sinoyd.parse.util.CharUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 解析流方案参数配置数据访问操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class StreamParamConfigServiceImpl extends BaseJpaServiceImpl<DtoStreamParamConfig, String, StreamParamConfigRepository> implements StreamParamConfigService {

    /*
     * 解析流方案参数信息数据访问操作接口
     */
    @Autowired
    private StreamParamConfigRepository streamParamConfigRepository;

    /*
     * 解析流方案信息数据访问操作接口
     */
    @Autowired
    private StreamConfigService streamConfigService;

    @Override
    public void findByPage(PageBean<DtoStreamParamConfig> pb, BaseCriteria streamParamConfigCriteria) {
        String planId = ((StreamParamConfigCriteria) streamParamConfigCriteria).getPlanId();
        if (StringUtils.isEmpty(planId)) {
            throw new BaseException("解析流方案id不存在!");
        }
        pb.setEntityName("DtoStreamParamConfig a");
        pb.setSelect("select new com.sinoyd.parse.dto.DtoStreamParamConfig(a.id, a.paramName, a.paramAlias, a.paramType)");
        super.findByPage(pb, streamParamConfigCriteria);
    }


    @Override
    public DtoStreamParamConfig findStreamParamConfig(String id) {
        DtoStreamParamConfig dtoStreamParamConfig = super.findOne(id);
        if (StringUtils.isNull(dtoStreamParamConfig) || dtoStreamParamConfig.getIsDeleted()) {
            throw new BaseException("解析流方案参数配置信息不存在!");
        }
        return dtoStreamParamConfig;
    }

    @Override
    @Transactional
    public DtoStreamParamConfig saveStreamParamConfig(DtoStreamParamConfig entity) {
        String planId = entity.getPlanId();
        if (StringUtils.isEmpty(planId)) {
            throw new BaseException("解析方案配置id不能为空!");
        }
        DtoStreamConfig dtoStreamConfig = streamConfigService.findOne(planId);
        if (StringUtils.isNull(dtoStreamConfig) || dtoStreamConfig.getIsDeleted()) {
            throw new BaseException("解析方案配置不存在或已删除!");
        }
        if (StringUtils.isNull(entity) || StringUtils.isEmpty(entity.getParamName())) {
            throw new BaseException("解析方案参数配置名称不能为空!");
        }
        //若新名称长度超出限制则直接抛出异常
        if (entity.getParamName().length() > 25 && CharUtil.calByteLen(entity.getParamName(), 2, 1) > 50) {
            throw new BaseException("参数配置名称长度超出限制,不能新增！");
        }
        if (StringUtils.isNotEmpty(entity.getParamAlias()) && entity.getParamAlias().length() > 25
                && CharUtil.calByteLen(entity.getParamAlias(), 2, 1) > 50) {
            throw new BaseException("参数配置别名长度超出限制,不能新增！");
        }
        List<DtoStreamParamConfig> oriStreamParamConfigList = streamParamConfigRepository.findByPlanIdAndParamNameAndIsDeleted(planId, entity.getParamName(), false);
        if (StringUtils.isNotEmpty(oriStreamParamConfigList)) {
            throw new BaseException("存在同名参数!");
        }
        entity.setIsDeleted(false);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoStreamParamConfig updateStreamParamConfig(DtoStreamParamConfig entity) {
        if (StringUtils.isNull(entity) || StringUtils.isEmpty(entity.getParamName())) {
            throw new BaseException("解析流方案参数名称不能为空!");
        }
        //若新名称长度超出限制则直接抛出异常
        if (entity.getParamName().length() > 25 && CharUtil.calByteLen(entity.getParamName(), 2, 1) > 50) {
            throw new BaseException("参数配置名称长度超出限制,不能修改！");
        }
        if (StringUtils.isNotEmpty(entity.getParamAlias()) && entity.getParamAlias().length() > 25
                &&CharUtil.calByteLen(entity.getParamAlias(), 2, 1) > 50) {
            throw new BaseException("参数配置别名长度超出限制,不能修改！");
        }
        String id = entity.getId();
        if (StringUtils.isEmpty(id)) {
            throw new BaseException("解析流方案参数id不能为空!");
        }
        DtoStreamParamConfig oriStreamParamConfig = super.findOne(id);
        if (StringUtils.isNull(oriStreamParamConfig) || oriStreamParamConfig.getIsDeleted()) {
            throw new BaseException("该解析流方案参数已被删除!");
        }
        //若修改了参数名称则需要判断是否存在同名参数
        if (!entity.getParamName().equals(oriStreamParamConfig.getParamName())) {
            List<DtoStreamParamConfig> streamParamConfigList = streamParamConfigRepository.findByPlanIdAndParamNameAndIsDeleted(
                    oriStreamParamConfig.getPlanId(), entity.getParamName(), false);
            if (StringUtils.isNotEmpty(streamParamConfigList)) {
                throw new BaseException("解析流方案参数名称已存在!");
            }
        }
        entity.setIsDeleted(false);
        entity.setPlanId(oriStreamParamConfig.getPlanId());
        entity.setCreateDate(oriStreamParamConfig.getCreateDate());
        entity.setCreator(oriStreamParamConfig.getCreator());
        return repository.save(entity);
    }

    @Override
    @Transactional
    public Integer deleteByIds(List<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return 0;
        }
        return super.logicDeleteById(ids);
    }

}