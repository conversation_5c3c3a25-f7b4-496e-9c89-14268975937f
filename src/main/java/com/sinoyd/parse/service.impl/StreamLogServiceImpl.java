package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.repository.CommonRepository;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.repository.OrgStreamRepository;
import com.sinoyd.parse.repository.StreamDataRepository;
import com.sinoyd.parse.repository.StreamErrLogRepository;
import com.sinoyd.parse.repository.StreamLogRepository;
import com.sinoyd.parse.service.StreamLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 解析流日志数据访问操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class StreamLogServiceImpl extends BaseJpaServiceImpl<DtoStreamLog, String, StreamLogRepository> implements StreamLogService {

    /*
     * 解析流数据信息数据访问操作接口
     */
    @Autowired
    private StreamErrLogRepository streamErrLogRepository;


    /*
     * 源数据流信息数据访问操作接口
     */
    @Autowired
    private OrgStreamRepository orgStreamRepository;

    @Autowired
    private CommonRepository commonRepository;

    @Override
    public void findByPage(PageBean<DtoStreamLog> pb, BaseCriteria streamLogCriteria) {
        pb.setEntityName("DtoStreamLog a, DtoStreamApp b");
        pb.setSelect("select new com.sinoyd.parse.dto.DtoStreamLog(a.id, a.serialNumber, b.instrumentName, b.instrumentCode, a.getTime, a.parseTime, a.parseStatus)");
        this.comRepository.findByPage(pb, streamLogCriteria);
    }

    @Override
    public DtoOrgStream findSourceStream(String id) {
        return orgStreamRepository.findStreamContent(id);
    }

    @Override
    public DtoStreamDataErrLog findStreamDataErrLog(String id, String paramName) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select new com.sinoyd.parse.dto.DtoStreamData(a.paramName, a.paramAlias, a.value, a.unit, a.paramType, b.orderNum) ");
        sb.append(" from DtoStreamData a, DtoStreamParamConfig b ");
        sb.append(" where a.planId = b.planId ");
        sb.append(" and a.paramName = b.paramName ");
        sb.append(" and a.parseStreamLogId = :parseStreamLogId ");
        sb.append(" and a.isDeleted = 0 ");
        sb.append(" and b.isDeleted = 0 ");
        sb.append(" order by b.orderNum desc ");
        Map<String, Object> values = new HashMap<>();
        values.put("parseStreamLogId", id);
        List<DtoStreamData> streamDataList = commonRepository.find(sb.toString(), values);
        if (StringUtils.isNotEmpty(paramName)) {
            streamDataList = streamDataList.stream().filter(p -> StringUtils.isNotEmpty(p.getParamName()) && p.getParamName().contains(paramName)).collect(Collectors.toList());
        }
        List<DtoStreamErrLog> streamErrLogList = streamErrLogRepository.findByParseStreamId(id);
        DtoStreamDataErrLog dtoStreamDataErrLog = new DtoStreamDataErrLog();
        dtoStreamDataErrLog.setStreamData(StringUtils.isNotEmpty(streamDataList) ? streamDataList : new ArrayList<>());
        dtoStreamDataErrLog.setStreamErrLogs(StringUtils.isNotEmpty(streamErrLogList) ? streamErrLogList : new ArrayList<>());
        return dtoStreamDataErrLog;
    }

    @Override
    public DtoStreamErrLog findStreamErrLog(String id) {
        DtoStreamErrLog dtoStreamErrLog = streamErrLogRepository.findErrLogDetail(id);
        if (StringUtils.isNull(dtoStreamErrLog)) {
            throw new BaseException("解析流错误日志不存在或已删除!");
        }
        return dtoStreamErrLog;
    }
}