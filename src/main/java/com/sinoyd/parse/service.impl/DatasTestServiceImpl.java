package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoDatasTest;
import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.parse.enums.EnumParseStatus;
import com.sinoyd.parse.repository.DatasTestRepository;
import com.sinoyd.parse.service.DatasTestService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;


/**
 * DatasTest操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class DatasTestServiceImpl extends BaseJpaServiceImpl<DtoDatasTest,String,DatasTestRepository> implements DatasTestService {

    @Override
    public void findByPage(PageBean<DtoDatasTest> pb, BaseCriteria datasTestCriteria) {
        pb.setEntityName("DtoDatasTest a, DtoLog b");
        pb.setSelect("select a, b");
        comRepository.findByPage(pb, datasTestCriteria);
        List list = pb.getData();
        Iterator iterator = list.iterator();
        List<DtoDatasTest> datasList = new ArrayList<>();
        Map<String, String> parseStatusMap = EnumParseStatus.getMapData();
        while (iterator.hasNext()) {
            Object[] objects = (Object[]) iterator.next();
            DtoDatasTest datas = (DtoDatasTest) objects[0];
            DtoLog log = (DtoLog) objects[1];
            datas.setFileName(log.getFileOrgName());
            String parseStatus = log.getParseStatus();
            if (StringUtils.isNotEmpty(parseStatus)) {
                datas.setParseStatusName(parseStatusMap.getOrDefault(parseStatus, ""));
            }
            datasList.add(datas);
        }
        pb.setData(datasList);
    }
}