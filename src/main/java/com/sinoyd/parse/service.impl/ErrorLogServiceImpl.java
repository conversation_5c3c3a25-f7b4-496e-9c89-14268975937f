package com.sinoyd.parse.service.impl;

import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoErrorLog;
import com.sinoyd.parse.repository.ErrorLogRepository;
import com.sinoyd.parse.service.ErrorLogService;
import org.springframework.stereotype.Service;


/**
 * ErrorLog操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class ErrorLogServiceImpl extends BaseJpaServiceImpl<DtoErrorLog,String,ErrorLogRepository> implements ErrorLogService {
    @Override
    public void findByPage(PageBean<DtoErrorLog> pb, BaseCriteria errorLogCriteria) {
        pb.setEntityName("DtoErrorLog a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, errorLogCriteria);
    }

    @Override
    public DtoErrorLog findErrorLog(String parselogId) {
        return repository.findByParselogId(parselogId).stream().findFirst().orElse(null);
    }
}