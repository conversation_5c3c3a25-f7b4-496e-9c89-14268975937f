package com.sinoyd.parse.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.jsoniter.spi.TypeLiteral;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.JsonUtil;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.frame.base.util.JsonUtils;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.enums.*;
import com.sinoyd.parse.repository.AiParseFileAppRepository;
import com.sinoyd.parse.repository.AppFlowDataRepository;
import com.sinoyd.parse.repository.ErrorLogRepository;
import com.sinoyd.parse.repository.ParseLogRepository;
import com.sinoyd.parse.service.AiParseDataHandleService;
import com.sinoyd.parse.vo.AiParseParamDataVO;
import com.sinoyd.parse.vo.AiParseStepVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AI解析数据处理服务接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/28
 **/
@Service
@Slf4j
@SuppressWarnings({"unchecked", "rawtypes"})
public class AiParseDataHandleServiceImpl implements AiParseDataHandleService {

    private ParseLogRepository parseLogRepository;

    private AppFlowDataRepository appFlowDataRepository;

    private ErrorLogRepository errorLogRepository;

    private AiParseFileAppRepository aiParseFileAppRepository;

    private RedisTemplate redisTemplate;

    /**
     * AI解析数据Redis Key前缀
     */
    public static final String AI_PARSE_DATA_REDIS_KEY = "AiInstrumentParse:ParseData:";

    /**
     * AI解析日志Redis Key前缀
     */
    public static final String AI_PARSE_LOG_REDIS_KEY = "AiInstrumentParse:ParseLog:";

    /**
     * AI解析步骤日志Redis Key前缀
     */
    public static final String AI_PARSE_STEP_LOG_REDIS_KEY = "AiInstrumentParse:ParseStepLog:";

    /**
     * AI解析异常步骤日志Redis Key前缀
     */
    public static final String AI_PARSE_STEP_FAIL_LOG_REDIS_KEY = "AiInstrumentParse:ParseStepFailLog:";

    @Override
    public List<AiParseParamDataVO> findAppParseDataRedisByAppId(String appId) {
        //先从Redis中获取数据
        String redisKey = AI_PARSE_DATA_REDIS_KEY + appId;
        if (redisTemplate.hasKey(redisKey)) {
            Object redisObj = redisTemplate.opsForValue().get(redisKey);
            if (redisObj != null) {
                TypeLiteral<List<AiParseParamDataVO>> typeLiteral = new TypeLiteral<List<AiParseParamDataVO>>() {
                };
                return JsonUtils.deserialize(redisObj.toString(), typeLiteral);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<DtoAppFlowData> findAppFlowDataRedisByAppId(String appId) {
        //先从Redis中获取数据
        String redisKey = AI_PARSE_STEP_LOG_REDIS_KEY + appId;
        if (redisTemplate.hasKey(redisKey)) {
            Object redisObj = redisTemplate.opsForValue().get(redisKey);
            if (redisObj != null) {
                TypeLiteral<List<DtoAppFlowData>> typeLiteral = new TypeLiteral<List<DtoAppFlowData>>() {
                };
                return JsonUtils.deserialize(redisObj.toString(), typeLiteral);
            }
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional
    public void batchSaveRedisDB(Collection<String> appIds) {
        //解析全部结束，持久化所有Redis数据并清空相关Redis数据（这里不保存结果数据，只有在，点击保存按钮之后，才持久化保存结果数据）

        List<DtoAiParseFileApp> appList = aiParseFileAppRepository.findAllById(appIds);
        //Map
        Map<String, DtoAiParseFileApp> appMap = appList.stream().collect(Collectors.toMap(DtoAiParseFileApp::getId, dto -> dto));
        //需要更新的应用数据
        List<DtoAiParseFileApp> updateAppList = new ArrayList<>();

        //需要保存的参数数据
        List<DtoLog> saveLogs = new ArrayList<>();
        //需要保存的解析步骤数据
        List<DtoAppFlowData> saveFlowDataList = new ArrayList<>();
        //需要保存的异常步骤数据
        List<DtoErrorLog> saveErrorLogList = new ArrayList<>();
        //循环获取Redis数据
        appIds.forEach(appId -> {
            DtoAiParseFileApp parseFileApp = appMap.get(appId);
            //获取解析日志数据
            String logRedisKey = AI_PARSE_LOG_REDIS_KEY + appId;
            if (redisTemplate.hasKey(logRedisKey)) {
                Object logRedis = redisTemplate.opsForValue().get(logRedisKey);
                if (logRedis != null) {
                    DtoLog log = JsonUtils.deserialize(logRedis.toString(), DtoLog.class);
                    if (log != null) {
                        EnumParseLogStatus logParseStatus = EnumParseLogStatus.getEnumByValue(log.getParseStatus());
                        switch (logParseStatus) {
                            case SUCCESS:
                                parseFileApp.setParseStatus(EnumAIParseStatus.SUCCESS.getValue());
                                break;
                            case FAIL:
                                parseFileApp.setParseStatus(EnumAIParseStatus.FAIL.getValue());
                                break;
                        }
                        saveLogs.add(log);
                    }
                }

            }
            //获取解析步骤数据
            String flowDataRedisKey = AI_PARSE_STEP_LOG_REDIS_KEY + appId;
            if (redisTemplate.hasKey(flowDataRedisKey)) {
                Object flowDataRedis = redisTemplate.opsForValue().get(flowDataRedisKey);
                if (flowDataRedis != null) {
                    TypeLiteral<List<DtoAppFlowData>> typeLiteral = new TypeLiteral<List<DtoAppFlowData>>() {
                    };
                    List<DtoAppFlowData> flowDataList = JsonUtils.deserialize(flowDataRedis.toString(), typeLiteral);
                    if (StringUtils.isNotEmpty(flowDataList)) {
                        saveFlowDataList.addAll(flowDataList);
                    }
                }

            }
            //获取异常步骤数据
            String errorLogRedisKey = AI_PARSE_STEP_FAIL_LOG_REDIS_KEY + appId;
            if (redisTemplate.hasKey(errorLogRedisKey)) {
                Object errorLogRedis = redisTemplate.opsForValue().get(errorLogRedisKey);
                if (errorLogRedis != null) {
                    TypeLiteral<List<DtoErrorLog>> typeLiteral = new TypeLiteral<List<DtoErrorLog>>() {
                    };
                    List<DtoErrorLog> errorLogList = JsonUtils.deserialize(errorLogRedis.toString(), typeLiteral);
                    if (StringUtils.isNotEmpty(errorLogList)) {
                        saveErrorLogList.addAll(errorLogList);
                    }
                }
            }
            updateAppList.add(parseFileApp);
        });
        //持久化保存Redis数据
        //保存解析日志数据
        if (StringUtils.isNotEmpty(saveLogs)) {
            parseLogRepository.saveAll(saveLogs);
        }
        //保存解析步骤数据
        if (StringUtils.isNotEmpty(saveFlowDataList)) {
            appFlowDataRepository.saveAll(saveFlowDataList);
        }
        //保存异常步骤数据
        if (StringUtils.isNotEmpty(saveErrorLogList)) {
            errorLogRepository.saveAll(saveErrorLogList);
        }
        //更新应用解析状态
        if (StringUtils.isNotEmpty(updateAppList)) {
            aiParseFileAppRepository.saveAll(updateAppList);
        }
        //清空Redis数据
        clearParseRedis(appIds);
    }

    @Override
    public DtoLog saveLogRedis(DtoAiParseFileApp parseApp) {
        //日志RedisKey
        String logRedisKey = AI_PARSE_LOG_REDIS_KEY + parseApp.getId();
        //将解析日志存入redis中
        DtoLog parseLog = new DtoLog();
        parseLog.setParseStatus(EnumParseLogStatus.SUCCESS.getValue());
        parseLog.setAppId(parseApp.getId());
        DtoParseDocument document = parseApp.getDocument();
        if (document != null) {
            parseLog.setFileOrgFolderName(document.getFolderName());
            parseLog.setDestFolderName(document.getFolderName());
            parseLog.setFileOrgName(document.getFilename());
            parseLog.setFileName(document.getPhysicalName());
        }
        parseLog.setParseType(EnumParseType.AI.getValue());
        parseLog.setHandleType(EnumHandleType.PARSE.getName());
        parseLog.setLogContent("AI解析日志");
        parseLog.setBeginTime(new Date());
        redisTemplate.opsForValue().set(logRedisKey, JsonUtils.serialize(parseLog));
        return parseLog;
    }

    @Override
    public void saveStepLogRedis(String appId, String logId, AiParseStepVO stepVO) {
        //步骤日志RedisKey
        String stepLogRedisKey = AI_PARSE_STEP_LOG_REDIS_KEY + appId;
        //日志RedisKey
        String logRedisKey = AI_PARSE_LOG_REDIS_KEY + appId;
        //异常步骤日志RedisKey
        String errorLogRedisKey = AI_PARSE_STEP_FAIL_LOG_REDIS_KEY + appId;
        //步骤数据列表
        List<DtoAppFlowData> flowDataList = new ArrayList<>();
        //异常步骤日志列表
        List<DtoErrorLog> errorLogList = new ArrayList<>();
        //从Redis中获取应用下的所有解析步骤数据（如果没有则新建）
        if (redisTemplate.hasKey(stepLogRedisKey)) {
            Object flowDataRedis = redisTemplate.opsForValue().get(stepLogRedisKey);
            if (flowDataRedis != null) {
                TypeLiteral<List<DtoAppFlowData>> typeLiteral = new TypeLiteral<List<DtoAppFlowData>>() {
                };
                flowDataList = JsonUtils.deserialize(flowDataRedis.toString(), typeLiteral);
            }
        }
        //如果Redis中没有数据则新建集合，防止后续报错
        if (flowDataList == null) {
            flowDataList = new ArrayList<>();
        }
        //获取步骤的日志数据
        DtoLog log = null;
        if (redisTemplate.hasKey(logRedisKey)) {
            Object logRedis = redisTemplate.opsForValue().get(logRedisKey);
            if (logRedis != null) {
                log = JsonUtils.deserialize(logRedis.toString(), DtoLog.class);
            }
            if (stepVO.getIsError() && log != null) {
                log.setLogContent("AI解析异常!" + stepVO.getMsg());
                log.setParseStatus(EnumParseLogStatus.FAIL.getValue());
            }
        }
        if (log != null) {
            flowDataList.add(createFlowData(log, stepVO));
            //如果过程异常，则多保存一个异常数据
            if (stepVO.getIsError()) {
                errorLogList.add(createErrorStepLog(log, stepVO));
            }
            //设置日志解析结束时间
            if (stepVO.getIsFinish() || stepVO.getIsError()) {
                log.setEndTime(new Date());
            }
            //将解析日志数据重新存入redis中
            redisTemplate.opsForValue().set(logRedisKey, JsonUtils.serialize(log));
        }
        //将解析步骤数据重新存入redis中
        redisTemplate.opsForValue().set(stepLogRedisKey, JsonUtils.serialize(flowDataList));
        //将解析错误步骤数据重新存入redis中
        redisTemplate.opsForValue().set(errorLogRedisKey, JsonUtils.serialize(errorLogList));
    }

    @Override
    public List<AiParseParamDataVO> saveParseDataRedis(String appId, String logId, JSONObject dataJsObj) {
        //规定AI回答的数据格式，{"参数列表":[{"样品编号’:"SY2024082101’,"化合物名称’:"F’, ...},{}]}
        //根据特定格式进行数据获取
        List<AiParseParamDataVO> parseDataList = new ArrayList<>();
        //获取结果数据
        try {
            //获取AI回答结果
            JSONObject aiResultJsObj = dataJsObj.getJSONObject("workflow_result");
            //获取参数列表
            JSONArray sampleList = aiResultJsObj.getJSONArray("参数列表");
            for (Object object : sampleList) {
                JSONObject paramsObj = (JSONObject) object;
                AiParseParamDataVO paramDataVO = new AiParseParamDataVO();

                paramDataVO.setAppId(appId);
                paramDataVO.setParseLogId(logId);
                paramDataVO.setInstrumentCode(paramsObj.getString("仪器编号"));
                paramDataVO.setGatherCode(paramsObj.getString("样品编号"));
                paramDataVO.setAnalyzeItem(paramsObj.getString("分析项目"));
                paramDataVO.setParamName(paramsObj.getString("参数名称"));
                paramDataVO.setSaveValue(paramsObj.getString("参数值"));
                paramDataVO.setUnit(paramsObj.getString("单位"));
                paramDataVO.setParseDateTime(DateUtil.dateToString(new Date(), DateUtil.FULL));

                parseDataList.add(paramDataVO);
            }
        } catch (JSONException | NullPointerException e) {
            log.error("AI解析数据转换失败：{}", e.getMessage(), e);
            throw new JSONException("AI解析数据转换失败，请确保AI输出数据格式正确!{\"参数列表\":[{\"样品编号’:\"SY2024082101’,\"化合物名称’:\"F’, ...},{}]}");
        }
        //TODO:将解析数据存入redis中
        redisTemplate.opsForValue().set(AI_PARSE_DATA_REDIS_KEY + appId, JsonUtils.serialize(parseDataList));
        return parseDataList;
    }

    @Override
    public void deleteRedisByKey(Collection<String> appIds, String redisType) {
        List<String> deleteRedisKeys = new ArrayList<>();
        appIds.forEach(appId -> deleteRedisKeys.add(redisType + appId));
        if (StringUtils.isNotEmpty(deleteRedisKeys)) {
            redisTemplate.delete(deleteRedisKeys);
        }
    }

    @Override
    public void deleteRedis(Collection<String> appIds) {
        //清空AI解析过程相关Redis数据
        clearParseRedis(appIds);
        //删除AI解析数据Redis
        deleteRedisByKey(appIds, AI_PARSE_DATA_REDIS_KEY);
    }

    /**
     * 清空AI解析过程相关Redis数据
     * 1.AI解析日志数据Redis
     * 2.AI解析步骤数据Redis
     * 3.AI解析异常步骤数据Redis
     *
     * @param appIds AI解析应用id集合
     */
    private void clearParseRedis(Collection<String> appIds) {
        //删除AI解析日志数据Redis
        deleteRedisByKey(appIds, AI_PARSE_LOG_REDIS_KEY);
        //删除AI解析步骤数据Redis
        deleteRedisByKey(appIds, AI_PARSE_STEP_LOG_REDIS_KEY);
        //删除AI解析异常步骤数据Redis
        deleteRedisByKey(appIds, AI_PARSE_STEP_FAIL_LOG_REDIS_KEY);
    }

    /**
     * 创建解析错误步骤日志实体
     *
     * @param log    AI解析日志实体
     * @param stepVO AI解析步骤实体
     * @return 解析错误日志实体
     */
    private DtoErrorLog createErrorStepLog(DtoLog log, AiParseStepVO stepVO) {
        DtoErrorLog errorLog = new DtoErrorLog();
        errorLog.setParselogId(log.getId());
        errorLog.setFlowId(stepVO.getStepId());
        errorLog.setFlowName(stepVO.getStepName());
        errorLog.setErrorType(stepVO.getErrorType());
        errorLog.setErrorTime(new Date());
        errorLog.setLogContent(stepVO.getMsg().toString());
        return errorLog;
    }

    /**
     * 创建解析流程日志实体
     *
     * @param log    AI解析日志实体
     * @param stepVO AI解析步骤实体
     * @return 解析流程日志实体
     */
    private DtoAppFlowData createFlowData(DtoLog log, AiParseStepVO stepVO) {
        DtoAppFlowData flowData = new DtoAppFlowData();
        flowData.setParselogId(log.getId());
        flowData.setFlowName(stepVO.getStepName());
        flowData.setParseStatus(stepVO.getIsError() ? EnumParseStatus.FAIL.getValue() : EnumParseStatus.SUCCESS.getValue());
        flowData.setParseData(JsonUtils.serialize(stepVO.getMsg()));
        flowData.setParseTime(new Date());
        return flowData;
    }

    @Autowired
    public void setParseLogRepository(ParseLogRepository parseLogRepository) {
        this.parseLogRepository = parseLogRepository;
    }

    @Autowired
    public void setAppFlowDataRepository(AppFlowDataRepository appFlowDataRepository) {
        this.appFlowDataRepository = appFlowDataRepository;
    }

    @Autowired
    public void setErrorLogRepository(ErrorLogRepository errorLogRepository) {
        this.errorLogRepository = errorLogRepository;
    }

    @Autowired
    public void setAiParseFileAppRepository(AiParseFileAppRepository aiParseFileAppRepository) {
        this.aiParseFileAppRepository = aiParseFileAppRepository;
    }

    @Autowired
    public void setRedisTemplate(RedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
}
