package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoAiInstrumentConfig;
import com.sinoyd.parse.enums.EnumInstrumentType;
import com.sinoyd.parse.repository.AiInstrumentConfigRepository;
import com.sinoyd.parse.service.AiInstrumentConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * AI仪器解析配置操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/07
 **/
@Service
public class AiInstrumentConfigServiceImpl extends BaseJpaServiceImpl<DtoAiInstrumentConfig, String, AiInstrumentConfigRepository> implements AiInstrumentConfigService {

    @Override
    public void findByPage(PageBean<DtoAiInstrumentConfig> pb, BaseCriteria aiInstrumentConfigCriteria) {
        pb.setEntityName("DtoAiInstrumentConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiInstrumentConfigCriteria);
        
        // 设置仪器类型名称
        List<DtoAiInstrumentConfig> configList = pb.getData();
        Map<String, String> instrumentTypeMap = EnumInstrumentType.getMapData();
        for (DtoAiInstrumentConfig config : configList) {
            String instrumentType = config.getInstrumentType();
            if (StringUtils.isNotEmpty(instrumentType)) {
                config.setInstrumentTypeName(instrumentTypeMap.getOrDefault(instrumentType, ""));
            }
        }
    }

    @Override
    @Transactional
    public DtoAiInstrumentConfig save(DtoAiInstrumentConfig entity) {
        // 校验仪器名称重复
        checkInstrumentNameDuplicate(entity);
        // 校验仪器编号重复
        checkInstrumentCodeDuplicate(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiInstrumentConfig update(DtoAiInstrumentConfig entity) {
        // 校验仪器名称重复
        checkInstrumentNameDuplicate(entity);
        // 校验仪器编号重复
        checkInstrumentCodeDuplicate(entity);
        return super.update(entity);
    }

    /**
     * 校验仪器名称重复
     *
     * @param entity 实体
     */
    private void checkInstrumentNameDuplicate(DtoAiInstrumentConfig entity) {
        if (StringUtils.isNotEmpty(entity.getInstrumentName())) {
            Integer count = repository.countByInstrumentNameAndIdNot(entity.getInstrumentName(), entity.getId());
            if (StringUtils.isNotNull(count) && count > 0) {
                throw new BaseException("仪器名称已存在：" + entity.getInstrumentName());
            }
        }
    }

    /**
     * 校验仪器编号重复
     *
     * @param entity 实体
     */
    private void checkInstrumentCodeDuplicate(DtoAiInstrumentConfig entity) {
        if (StringUtils.isNotEmpty(entity.getInstrumentCode())) {
            Integer count = repository.countByInstrumentCodeAndIdNot(entity.getInstrumentCode(), entity.getId());
            if (StringUtils.isNotNull(count) && count > 0) {
                throw new BaseException("仪器编号已存在：" + entity.getInstrumentCode());
            }
        }
    }
}
