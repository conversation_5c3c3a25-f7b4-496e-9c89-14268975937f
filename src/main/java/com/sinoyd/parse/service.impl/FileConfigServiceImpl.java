package com.sinoyd.parse.service.impl;

import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.dto.customer.DtoExpImpFileConfigAlais;
import com.sinoyd.parse.dto.customer.DtoExpImpFileConfigApp;
import com.sinoyd.parse.dto.customer.DtoExpImpFileConfigFlow;
import com.sinoyd.parse.enums.EnumConfigFileType;
import com.sinoyd.parse.enums.EnumHandleType;
import com.sinoyd.parse.repository.FileAppRepository;
import com.sinoyd.parse.repository.FileConfigRepository;
import com.sinoyd.parse.repository.FileFlowRepository;
import com.sinoyd.parse.repository.FileResultAlaisRepository;
import com.sinoyd.parse.service.FileAppService;
import com.sinoyd.parse.service.FileConfigService;
import com.sinoyd.parse.service.FileFlowService;
import com.sinoyd.parse.util.ImportUtils;
import com.sinoyd.parse.util.PoiExcelUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * FileConfig操作接口
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@Slf4j
@Service
public class FileConfigServiceImpl extends BaseJpaServiceImpl<DtoFileConfig, String, FileConfigRepository> implements FileConfigService {
    @Autowired
    private FileFlowService fileFlowService;
    @Autowired
    private FileAppService fileAppService;
    @Autowired
    private FileResultAlaisRepository resultAlaisRepository;
    @Autowired
    private FileFlowRepository fileFlowRepository;
    @Autowired
    private FileResultAlaisRepository fileResultAlaisRepository;
    @Autowired
    private FileAppRepository fileAppRepository;

    private ImportUtils importUtils;

    @Value("${fileProps.filePath}")
    private String filePath;

    @Override
    public void findByPage(PageBean<DtoFileConfig> pb, BaseCriteria fileConfigCriteria) {
        pb.setEntityName("DtoFileConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fileConfigCriteria);
        List<DtoFileConfig> fileConfigs = pb.getData();
        Map<String, String> fileTypeMap = EnumConfigFileType.getMapData();
        Map<String, String> handleTypeMap = EnumHandleType.getMapData();
        for (DtoFileConfig fileConfig : fileConfigs) {
            String fileType = fileConfig.getFileType();
            String handleType = fileConfig.getHandleType();
            if (StringUtils.isNotEmpty(fileType)) {
                fileConfig.setFileTypeName(fileTypeMap.getOrDefault(fileType, ""));
            }
            if (StringUtils.isNotEmpty(handleType)) {
                fileConfig.setHandleTypeName(handleTypeMap.getOrDefault(handleType, ""));
            }
        }
        pb.setData(fileConfigs);
    }

    @Override
    @Transactional
    public DtoFileConfig save(DtoFileConfig entity) {
        checkExist(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoFileConfig update(DtoFileConfig entity) {
        checkExist(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return 0;
        }
        List<String> configIds = ids.parallelStream().map(Object::toString).collect(Collectors.toList());
        // 流程还有关联参数,因此调用业务删除方法
        fileFlowService.deleteByConfigIds(configIds);
        // 应用配置有相关解析日志,调用业务删除
        fileAppService.deleteByConfigIds(configIds);
        // 结果映射无关联关系,直接调用删除
        resultAlaisRepository.deleteByConfigIds(configIds, new Date());
        return super.logicDeleteById(ids);
    }

    /**
     * 查重方法
     *
     * @param entity 实体
     */
    private void checkExist(DtoFileConfig entity) {
        Integer count = repository.countByPlanNameAndHandleTypeAndIdNot(entity.getPlanName(),
                entity.getHandleType(), entity.getId());
        if (StringUtils.isNotNull(count) && count > 0) {
            throw new BaseException("存在同名方案：" + entity.getPlanName());
        }
    }

    @Transactional
    @Override
    public String upload(HttpServletRequest request, String configId) {
        DtoFileConfig fileConfig = super.findOne(configId);
        if (StringUtils.isNull(fileConfig)) {
            throw new BaseException("方案不存在!");
        }
        MultipartFile multipartFile = ((MultipartHttpServletRequest) request).getFile("file");
        String newPath = "/fileConfigAttach";
        //创建文件目录
        String checkPath = filePath + newPath;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        //后缀名
        String fileName = multipartFile.getOriginalFilename();
        //物理名称
        String physicalName = dateFormat.format(new Date()) + "_" + fileName;
        String downPath = newPath + "/" + physicalName;
//        String fileType = fileName.substring(fileName.lastIndexOf("."));

        File file = new File(new File(checkPath).getAbsoluteFile() + File.separator + physicalName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try {
            multipartFile.transferTo(file);
            fileConfig.setPlanFile(downPath);
            repository.save(fileConfig);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return fileName;
    }

    @Override
    public String download(String configId, HttpServletResponse response) {
        DtoFileConfig fileConfig = super.findOne(configId);
        if (StringUtils.isNull(fileConfig)) {
            throw new BaseException("方案不存在!");
        }
        String path = filePath + fileConfig.getPlanFile();
        String fileName = path.substring(path.lastIndexOf("/") + 1, path.length());
        File file = new File(path);
        if (!file.exists()) {
            throw new BaseException("文件不存在,请确认");
        }
        downFile(file, response, fileName);
        return fileName;
    }

    /**
     * 文件下载
     *
     * @param file     文件对象
     * @param fileName 文件名
     * @param response 方案id
     */
    static void downFile(File file, HttpServletResponse response, String fileName) {
        FileInputStream fileInputStream;
        try {
            fileInputStream = new FileInputStream(file);
            response.setHeader("Content-Disposition", "attachment;Filename=" + URLEncoder.encode(fileName, "UTF-8"));
            OutputStream outputStream = response.getOutputStream();
            byte[] bytes = new byte[2048];
            int len;
            while ((len = fileInputStream.read(bytes)) > 0) {
                outputStream.write(bytes, 0, len);
            }
            fileInputStream.close();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<DtoDocument> getAttachInfo(String configId) {
        List<DtoDocument> dtoDocumentList = new ArrayList<>();
        DtoFileConfig fileConfig = super.findOne(configId);
        if (StringUtils.isNull(fileConfig)) {
            throw new BaseException("方案不存在!");
        }
        String filePlan = fileConfig.getPlanFile();
        if (StringUtils.isEmpty(filePlan)) {
            return dtoDocumentList;
        }
        int lineIdx = filePlan.indexOf("_");
        int dotIndex = filePlan.lastIndexOf(".");
        String fileName = filePlan.substring(lineIdx + 1, dotIndex);
        String fileType = filePlan.substring(dotIndex);
        DtoDocument dtoDocument = new DtoDocument();
        dtoDocument.setConfigId(configId);
        dtoDocument.setFileName(fileName);
        dtoDocument.setFileType(fileType);
        dtoDocument.setFilePath(filePlan);
        dtoDocumentList.add(dtoDocument);
        return dtoDocumentList;
    }

    @Transactional
    @Override
    public void deleteAttachInfo(String configId) {
        DtoFileConfig fileConfig = super.findOne(configId);
        if (StringUtils.isNull(fileConfig)) {
            throw new BaseException("方案不存在!");
        }
        fileConfig.setPlanFile("");
        repository.save(fileConfig);
    }

    @Override
    @Transactional
    public void importConfig(MultipartFile file, HttpServletResponse response) {
        PoiExcelUtils.verifyFileType(file);
        List<DtoFileConfig> insertFileConfigList = new ArrayList<>();
        List<DtoFileFlow> insertFileFlowList = new ArrayList<>();
        List<DtoFileResultAlais> insertFileAlsList = new ArrayList<>();
        List<DtoFileApp> insertFileAppList = new ArrayList<>();
        try {
            ExcelImportResult<DtoExpImpFileConfigFlow> flowResult = importUtils.getExcelData(file, DtoExpImpFileConfigFlow.class, 0, 1, 0, false, null);
            getFileFlowData(insertFileConfigList, insertFileFlowList, flowResult.getList());
            ExcelImportResult<DtoExpImpFileConfigAlais> alsResult = importUtils.getExcelData(file, DtoExpImpFileConfigAlais.class, 0, 1, 1, false, null);
            getFileAlsData(insertFileConfigList, insertFileAlsList, alsResult.getList());
            ExcelImportResult<DtoExpImpFileConfigApp> appResult = importUtils.getExcelData(file, DtoExpImpFileConfigApp.class, 0, 1, 2, false, null);
            getFileAppData(insertFileConfigList, insertFileAppList, appResult.getList());
        } catch (Exception e) {
            log.info("解析方案导入出错！", e);
            throw new BaseException("解析方案导入出错！");
        }
        insertFileConfigList = new ArrayList<>(insertFileConfigList.stream().collect(Collectors.toMap(DtoFileConfig::getId, dto -> dto, (c1, c2) -> c1)).values());
        if (StringUtils.isNotEmpty(insertFileConfigList)) {
            List<String> instConfigIdList = insertFileConfigList.stream().map(DtoFileConfig::getId).collect(Collectors.toList());
//            List<String> existDelConfigIdList = commonRepository.find("select c from DtoFileConfig c where c.id in :ids and c.isDeleted = 1", Collections.singletonMap("ids", instConfigIdList));
            List<String> existDelConfigIdList = repository.findDeleted(instConfigIdList).stream().map(DtoFileConfig::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(existDelConfigIdList)) {
                List<DtoFileConfig> configListForUpdate = insertFileConfigList.stream().filter(p -> existDelConfigIdList.contains(p.getId())).collect(Collectors.toList());
                for (DtoFileConfig configForUpdate : configListForUpdate) {
                    String oldId = configForUpdate.getId(), newId = UUIDHelper.newId();
                    configForUpdate.setId(newId);
                    List<DtoFileFlow> flowListForUpdate = insertFileFlowList.stream().filter(p -> oldId.equals(p.getPlanId())).collect(Collectors.toList());
                    flowListForUpdate.forEach(p -> p.setPlanId(newId));
                    List<DtoFileResultAlais> alsListForUpdate = insertFileAlsList.stream().filter(p -> oldId.equals(p.getPlanId())).collect(Collectors.toList());
                    alsListForUpdate.forEach(p -> p.setPlanId(newId));
                    List<DtoFileApp> appListForUpdate = insertFileAppList.stream().filter(p -> oldId.equals(p.getPlanId())).collect(Collectors.toList());
                    appListForUpdate.forEach(p -> p.setPlanId(newId));
                }
            }
            repository.saveAll(insertFileConfigList);
        }
        if (StringUtils.isNotEmpty(insertFileFlowList)) {
            List<String> instFlowIdList = insertFileFlowList.stream().map(DtoFileFlow::getId).collect(Collectors.toList());
            List<String> existDelFlowIdList = fileFlowRepository.findDeleted(instFlowIdList).stream().map(DtoFileFlow::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(existDelFlowIdList)) {
                List<DtoFileFlow> flowListForUpdate = insertFileFlowList.stream().filter(p -> existDelFlowIdList.contains(p.getId())).collect(Collectors.toList());
                flowListForUpdate.forEach(p -> p.setId(UUIDHelper.newId()));
            }
            fileFlowRepository.saveAll(insertFileFlowList);
        }
        if (StringUtils.isNotEmpty(insertFileAlsList)) {
            List<String> instRstIdList = insertFileAlsList.stream().map(DtoFileResultAlais::getId).collect(Collectors.toList());
            List<String> existDelRstIdList = fileResultAlaisRepository.findDeleted(instRstIdList).stream().map(DtoFileResultAlais::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(existDelRstIdList)) {
                List<DtoFileResultAlais> rstListForUpdate = insertFileAlsList.stream().filter(p -> existDelRstIdList.contains(p.getId())).collect(Collectors.toList());
                rstListForUpdate.forEach(p -> p.setId(UUIDHelper.newId()));
            }
            fileResultAlaisRepository.saveAll(insertFileAlsList);
        }
        if (StringUtils.isNotEmpty(insertFileAppList)) {
            List<String> instAppIdList = insertFileAppList.stream().map(DtoFileApp::getId).collect(Collectors.toList());
            List<String> existDelAppIdList = fileAppRepository.findDeleted(instAppIdList).stream().map(DtoFileApp::getId).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(existDelAppIdList)) {
                List<DtoFileApp> appListForUpdate = insertFileAppList.stream().filter(p -> existDelAppIdList.contains(p.getId())).collect(Collectors.toList());
                appListForUpdate.forEach(p -> p.setId(UUIDHelper.newId()));
            }
            fileAppRepository.saveAll(insertFileAppList);
        }
    }

    /**
     * 获取需要新增的解析方案流程配置
     *
     * @param insertFileConfigList 需要新增的解析方案对象
     * @param insertFileFlowList   需要新增的程配置对象
     */
    private void getFileFlowData(List<DtoFileConfig> insertFileConfigList, List<DtoFileFlow> insertFileFlowList, List<DtoExpImpFileConfigFlow> fileConfigFlowList) {
        fileConfigFlowList.removeIf(p -> StringUtils.isEmpty(p.getId()));
        if (StringUtils.isNotEmpty(fileConfigFlowList)) {
            for (DtoExpImpFileConfigFlow impConfigFlow : fileConfigFlowList) {
                String stepNumStr = impConfigFlow.getStepNumStr();
                impConfigFlow.setStepNum((StringUtils.isNotNull(stepNumStr) && ImportUtils.isNumeral(stepNumStr)) ? Integer.valueOf(stepNumStr) : null);
                String useLastDataStr = impConfigFlow.getUseLastDataStr();
                if (StringUtils.isNotEmpty(useLastDataStr)) {
                    impConfigFlow.setUseLastData("是".equals(useLastDataStr) ? true : ("否".equals(useLastDataStr) ? false : null));
                }
                String fileConfigId = impConfigFlow.getId();
                DtoFileConfig fileConfig = new DtoFileConfig();
                BeanUtils.copyProperties(impConfigFlow, fileConfig);
                insertFileConfigList.add(fileConfig);
                if (StringUtils.isNotEmpty(impConfigFlow.getFlowId()) && !UUIDHelper.guidEmpty().equals(impConfigFlow.getFlowId())) {
                    DtoFileFlow flow = new DtoFileFlow();
                    BeanUtils.copyProperties(impConfigFlow, flow, "id");
                    flow.setId(impConfigFlow.getFlowId());
                    flow.setPlanId(fileConfigId);
                    insertFileFlowList.add(flow);
                }
            }
        }
    }

    /**
     * 获取需要新增的解析方案结果映射
     *
     * @param insertFileConfigList 需要新增的解析方案对象
     * @param insertFileAlsList    需要新增的结果映射对象
     * @param fileConfigAlsList    导入的解析方案结果映射
     */
    private void getFileAlsData(List<DtoFileConfig> insertFileConfigList, List<DtoFileResultAlais> insertFileAlsList, List<DtoExpImpFileConfigAlais> fileConfigAlsList) {
        fileConfigAlsList.removeIf(p -> StringUtils.isEmpty(p.getId()));
        if (StringUtils.isNotEmpty(fileConfigAlsList)) {
            for (DtoExpImpFileConfigAlais impConfigAls : fileConfigAlsList) {
                String fileConfigId = impConfigAls.getId();
                DtoFileConfig fileConfig = new DtoFileConfig();
                BeanUtils.copyProperties(impConfigAls, fileConfig);
                insertFileConfigList.add(fileConfig);
                if (StringUtils.isNotEmpty(impConfigAls.getResultAliasId()) && !UUIDHelper.guidEmpty().equals(impConfigAls.getResultAliasId())) {
                    DtoFileResultAlais als = new DtoFileResultAlais();
                    BeanUtils.copyProperties(impConfigAls, als, "id");
                    als.setId(impConfigAls.getResultAliasId());
                    als.setPlanId(fileConfigId);
                    insertFileAlsList.add(als);
                }
            }
        }
    }

    /**
     * 获取需要新增的解析方案解析应用映射
     *
     * @param insertFileConfigList 需要新增的解析方案对象
     * @param insertFileAppList    需要新增的解析应用对象
     * @param fileAppList          导入的解析方案解析应用
     */
    private void getFileAppData(List<DtoFileConfig> insertFileConfigList, List<DtoFileApp> insertFileAppList, List<DtoExpImpFileConfigApp> fileAppList) {
        fileAppList.removeIf(p -> StringUtils.isEmpty(p.getId()));
        if (StringUtils.isNotEmpty(fileAppList)) {
            for (DtoExpImpFileConfigApp impConfigApp : fileAppList) {
                String fileConfigId = impConfigApp.getId();
                DtoFileConfig fileConfig = new DtoFileConfig();
                BeanUtils.copyProperties(impConfigApp, fileConfig);
                insertFileConfigList.add(fileConfig);
                if (StringUtils.isNotEmpty(impConfigApp.getAppId()) && !UUIDHelper.guidEmpty().equals(impConfigApp.getAppId())) {
                    DtoFileApp fileApp = new DtoFileApp();
                    BeanUtils.copyProperties(impConfigApp, fileApp, "id");
                    fileApp.setId(impConfigApp.getAppId());
                    fileApp.setPlanId(fileConfigId);
                    insertFileAppList.add(fileApp);
                }
            }
        }
    }

    @Override
    public void exportConfig(List<String> ids, HttpServletResponse response, String fileName) {
        List<DtoFileConfig> fileConfigList = super.findAll(ids);
        List<DtoExpImpFileConfigFlow> expImpFileConfigFlows = new ArrayList<>();
        List<DtoExpImpFileConfigAlais> expImpFileConfigAls = new ArrayList<>();
        List<DtoExpImpFileConfigApp> expImpFileConfigApp = new ArrayList<>();
        if (StringUtils.isNotEmpty(fileConfigList)) {
            List<String> configIdList = fileConfigList.stream().map(DtoFileConfig::getId).collect(Collectors.toList());
            Map<String, List<DtoFileFlow>> fileFlowMap = fileFlowRepository.findByPlanIdIn(configIdList).stream().collect(Collectors.groupingBy(DtoFileFlow::getPlanId));
            Map<String, List<DtoFileResultAlais>> resultAlsMap = fileResultAlaisRepository.findByPlanIdInAndIsDeletedFalse(configIdList)
                    .stream().collect(Collectors.groupingBy(DtoFileResultAlais::getPlanId));
            Map<String, List<DtoFileApp>> fileAppMap = fileAppRepository.findByPlanIdIn(configIdList)
                    .stream().collect(Collectors.groupingBy(DtoFileApp::getPlanId));
            for (DtoFileConfig fileConfig : fileConfigList) {
                List<DtoFileFlow> flowListForConfig = fileFlowMap.getOrDefault(fileConfig.getId(), new ArrayList<>());
                for (DtoFileFlow flow : flowListForConfig) {
                    DtoExpImpFileConfigFlow fileConfigFlow = new DtoExpImpFileConfigFlow();
                    BeanUtils.copyProperties(fileConfig, fileConfigFlow);
                    fileConfigFlow.setFlowId(flow.getId());
                    fileConfigFlow.setFlowName(flow.getFlowName());
                    fileConfigFlow.setParseType(flow.getParseType());
                    fileConfigFlow.setStepNumStr(String.valueOf(flow.getStepNum()));
                    fileConfigFlow.setStepNum(flow.getStepNum());
                    fileConfigFlow.setSpecialClass(flow.getSpecialClass());
                    fileConfigFlow.setUseLastData(flow.getUseLastData());
                    fileConfigFlow.setUseLastDataStr(StringUtils.isNotNull(flow.getUseLastData()) ? (flow.getUseLastData() ? "是" : "否") : null);
                    fileConfigFlow.setUseType(flow.getUseType());
                    expImpFileConfigFlows.add(fileConfigFlow);
                }
                if (StringUtils.isEmpty(flowListForConfig)) {
                    DtoExpImpFileConfigFlow fileConfigFlow = new DtoExpImpFileConfigFlow();
                    BeanUtils.copyProperties(fileConfig, fileConfigFlow);
                    expImpFileConfigFlows.add(fileConfigFlow);
                }
                List<DtoFileResultAlais> fileResultAlsForConfig = resultAlsMap.getOrDefault(fileConfig.getId(), new ArrayList<>());
                for (DtoFileResultAlais resultAls : fileResultAlsForConfig) {
                    DtoExpImpFileConfigAlais fileConfigAls = new DtoExpImpFileConfigAlais();
                    BeanUtils.copyProperties(fileConfig, fileConfigAls);
                    fileConfigAls.setResultAliasId(resultAls.getId());
                    fileConfigAls.setParamName(resultAls.getParamName());
                    fileConfigAls.setParamAlias(resultAls.getParamAlias());
                    fileConfigAls.setResultType(resultAls.getResultType());
                    expImpFileConfigAls.add(fileConfigAls);
                }
                if (StringUtils.isEmpty(fileResultAlsForConfig)) {
                    DtoExpImpFileConfigAlais fileConfigAls = new DtoExpImpFileConfigAlais();
                    BeanUtils.copyProperties(fileConfig, fileConfigAls);
                    expImpFileConfigAls.add(fileConfigAls);
                }
                List<DtoFileApp> fileAppForConfig = fileAppMap.getOrDefault(fileConfig.getId(), new ArrayList<>());
                for (DtoFileApp fileApp : fileAppForConfig) {
                    DtoExpImpFileConfigApp fileConfigApp = new DtoExpImpFileConfigApp();
                    BeanUtils.copyProperties(fileConfig, fileConfigApp);
                    fileConfigApp.setAppId(fileApp.getId());
                    fileConfigApp.setAppName(fileApp.getAppName());
                    fileConfigApp.setInstrumentId(fileApp.getInstrumentId());
                    fileConfigApp.setInstrumentName(fileApp.getInstrumentName());
                    fileConfigApp.setInstrumentCode(fileApp.getInstrumentCode());
                    fileConfigApp.setParseType(fileApp.getParseType());
                    fileConfigApp.setFolderName(fileApp.getFolderName());
                    fileConfigApp.setSucFolderName(fileApp.getSucFolderName());
                    fileConfigApp.setFailFolderName(fileApp.getFailFolderName());
                    expImpFileConfigApp.add(fileConfigApp);
                }
                if (StringUtils.isEmpty(fileAppForConfig)) {
                    DtoExpImpFileConfigApp fileConfigApp = new DtoExpImpFileConfigApp();
                    BeanUtils.copyProperties(fileConfig, fileConfigApp);
                    expImpFileConfigApp.add(fileConfigApp);
                }
            }
        }
        Map<String, List<?>> sheetDataMap = new LinkedHashMap<>();
        Map<String, Class<?>> sheetClassMap = new HashMap<>();
        sheetDataMap.put("解析方案-流程配置", expImpFileConfigFlows);
        sheetDataMap.put("解析方案-结果映射", expImpFileConfigAls);
        sheetDataMap.put("解析方案-解析应用", expImpFileConfigApp);
        sheetClassMap.put("解析方案-流程配置", DtoExpImpFileConfigFlow.class);
        sheetClassMap.put("解析方案-结果映射", DtoExpImpFileConfigAlais.class);
        sheetClassMap.put("解析方案-解析应用", DtoExpImpFileConfigApp.class);
        Workbook workBook = importUtils.getWorkBook(sheetDataMap, sheetClassMap);
        PoiExcelUtils.downLoadExcel(fileName, response, workBook);
    }

    @Autowired
    @Lazy
    public void setImportUtils(ImportUtils importUtils) {
        this.importUtils = importUtils;
    }
}