package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.dto.DtoOrg;
import com.sinoyd.frame.service.OrgService;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoDatas;
import com.sinoyd.parse.dto.DtoLog;
import com.sinoyd.parse.entity.Datas;
import com.sinoyd.parse.entity.FileApp;
import com.sinoyd.parse.repository.DatasRepository;
import com.sinoyd.parse.repository.FileAppRepository;
import com.sinoyd.parse.service.DatasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Datas操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class DatasServiceImpl extends BaseJpaServiceImpl<DtoDatas,String,DatasRepository> implements DatasService {
     @Autowired
     private FileAppRepository appRepository;

     @Autowired
     private OrgService orgService;

    @Override
    public void findByPage(PageBean<DtoDatas> pb, BaseCriteria datasCriteria) {
        pb.setEntityName("DtoDatas a, DtoLog b");
        pb.setSelect("select a,b");
        comRepository.findByPage(pb, datasCriteria);
        List list = pb.getData();
        Iterator iterator = list.iterator();
        List<DtoDatas> datasList = new ArrayList<>();
        while (iterator.hasNext()) {
            Object[] objects = (Object[]) iterator.next();
            DtoDatas datas = (DtoDatas) objects[0];
            DtoLog log = (DtoLog) objects[1];
            datas.setFileName(log.getFileOrgName());
            datasList.add(datas);
        }
        //设置app 名称
        List<String> appIds = datasList.parallelStream().map(Datas::getAppId).distinct().collect(Collectors.toList());
        //设置单位名称
        List<String> orgIds= datasList.stream().map(DtoDatas::getOrgId)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        List<DtoOrg> orgList = StringUtils.isNotEmpty(orgIds) ? orgService.findByOrgIds(orgIds) : new ArrayList<>();
        Map<String, DtoOrg> orgModelMap = orgList.stream().collect(Collectors.toMap(DtoOrg::getId, dto -> dto));
        Map<String, String> appNameMap = StringUtils.isNotEmpty(appIds) ? appRepository.findAllById(appIds)
                .parallelStream().collect(Collectors.toMap(FileApp::getId, FileApp::getAppName)) : new HashMap<>();

        for (DtoDatas data : datasList) {
            data.setAppName(appNameMap.getOrDefault(data.getAppId(), ""));
            String orgName = "";
            if (StringUtils.isNotEmpty(data.getOrgId()) && orgModelMap.containsKey(data.getOrgId())) {
                orgName = orgModelMap.get(data.getOrgId()).getOrgName();
            }
            data.setOrgName(orgName);
        }
        pb.setData(datasList);
    }

    @Override
    public List<DtoDatas> findBySampleCodes(Collection<String> sampleCodes) {
        return repository.findBySampleCodeIn(sampleCodes);
    }
}