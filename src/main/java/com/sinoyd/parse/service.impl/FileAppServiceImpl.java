package com.sinoyd.parse.service.impl;

import com.google.common.collect.Lists;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.frame.util.UUIDHelper;
import com.sinoyd.parse.dto.DtoFileApp;
import com.sinoyd.parse.dto.DtoFileConfig;
import com.sinoyd.parse.enums.EnumParseType;
import com.sinoyd.parse.repository.DatasRepository;
import com.sinoyd.parse.repository.DatasTestRepository;
import com.sinoyd.parse.repository.FileAppRepository;
import com.sinoyd.parse.repository.ParseLogRepository;
import com.sinoyd.parse.service.FileAppService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * FileApp操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class FileAppServiceImpl extends BaseJpaServiceImpl<DtoFileApp,String,FileAppRepository> implements FileAppService {

     @Autowired
     private ParseLogRepository logRepository;
     @Autowired
     private DatasRepository datasRepository;
     @Autowired
     private DatasTestRepository datasTestRepository;
     @Autowired
     private RedisTemplate redisTemplate;
     @Value("${fileProps.filePath}")
     private String filePath;
     @Value("${publish.channel}")
     private String channel;

    /**
     * 文件解析前缀
     */
    private static final String FILE_ANALYSIS_PREFIX = "FileAnalysis/";
    /**
     * 文件调试前缀
     */
    private static final String FILE_TEST_PREFIX = "FileTest/";
    /**
     * 解析成功目录前缀
     */
    private static final String SUCCESS_FOLDER_PREFIX = "success/";
    /**
     * 解析失败目录前缀
     */
    private static final String FAILURE_FOLDER_PREFIX = "failure/";

    @Override
    public void findByPage(PageBean<DtoFileApp> pb, BaseCriteria fileAppCriteria) {
        pb.setEntityName("DtoFileApp a, DtoFileConfig b");
        pb.setSelect("select a, b");
        comRepository.findByPage(pb, fileAppCriteria);
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        List list = pb.getData();
        Iterator iterator = list.iterator();
        List<DtoFileApp> results = new ArrayList<>();
        while (iterator.hasNext()) {
            Object[] objects = (Object[])iterator.next();
            DtoFileApp app = (DtoFileApp) objects[0];
            DtoFileConfig config = (DtoFileConfig) objects[1];
            String parseType = app.getParseType();
            String path = app.getFolderName();
            app.setPlanName(config.getPlanName());
            if (parseTypeMap.containsKey(parseType)) {
                app.setParseTypeName(parseTypeMap.get(parseType));
            }
            if (StringUtils.isNotEmpty(path)) {
                app.setFolderName(path.substring(path.indexOf("/")));
            }
            results.add(app);
        }
        pb.setData(results);
    }

    @Override
    @Transactional
    public DtoFileApp save(DtoFileApp entity) {
        checkExist(entity);
        initFolder(entity);
        List<DtoFileApp> entities = Lists.newArrayList(entity);
        if (EnumParseType.FILE.getValue().equals(entity.getParseType())) {
            DtoFileApp testApp = new DtoFileApp();
            BeanUtils.copyProperties(entity, testApp);
            entity.setFolderName(FILE_ANALYSIS_PREFIX + entity.getFolderName());
            testApp.setId(UUIDHelper.newId());
            testApp.setParseType(EnumParseType.FILE_TEST.getValue());
            testApp.setFolderName(FILE_TEST_PREFIX + testApp.getFolderName());
            entities.add(testApp);
        } else if (EnumParseType.FILE_TEST.getValue().equals(entity.getParseType())) {
            entity.setFolderName(FILE_ANALYSIS_PREFIX + entity.getFolderName());
        }
        super.save(entities);
        return entity;
    }

    @Override
    @Transactional
    public DtoFileApp update(DtoFileApp entity) {
        checkExist(entity);
        initFolder(entity);
        String parseType = entity.getParseType();
        if (EnumParseType.FILE.getValue().equals(parseType)) {
            entity.setFolderName(FILE_ANALYSIS_PREFIX + entity.getFolderName());
        } else if(EnumParseType.FILE_TEST.getValue().equals(parseType)) {
            entity.setFolderName(FILE_TEST_PREFIX + entity.getFolderName());
        }
        return super.update(entity);
    }

    @Override
    public Integer logicDeleteById(Collection<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return 0;
        }
        List<String> appIds = ids.parallelStream().map(Object::toString).collect(Collectors.toList());
        logRepository.deleteByAppIds(appIds, new Date());
        datasRepository.deleteByAppIds(appIds, new Date());
        datasTestRepository.deleteByAppIds(appIds, new Date());
        return super.logicDeleteById(ids);
    }

    @Override
    public void deleteByConfigIds(List<String> configIds) {
        List<DtoFileApp> apps = repository.findByPlanIdIn(configIds);
        if (StringUtils.isNotEmpty(apps)) {
            this.logicDeleteById(apps.parallelStream().map(DtoFileApp::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public Boolean upload(String appId, HttpServletRequest request) {
        if (StringUtils.isEmpty(appId)) {
            throw new BaseException("应用配置丢失,请刷新数据");
        }
        DtoFileApp app = super.findOne(appId);
        if (StringUtils.isNull(app)) {
           throw new BaseException("应用配置丢失,请刷新数据");
        }
        //redisKey前缀
        String sinoydParseFileRedisKey = "sinoydfile:";
        //获取当前操作人id
        String userId = PrincipalContextUser.getPrincipal().getUserId();
        List<MultipartFile> files = ((MultipartHttpServletRequest) request).getFiles("files");
        String dirPath = app.getFolderName();
        if (StringUtils.isEmpty(dirPath)) {
            throw new BaseException("文档路径配置错误");
        }
        // 去除前后/
        dirPath = this.suitFolder(dirPath);
        String parseType = app.getParseType();
        File fileDir = new File(filePath + "/" + dirPath);
        if (!fileDir.exists()) {
            fileDir.mkdirs();
        }
        for (MultipartFile file : files) {
            Path path = Paths.get(filePath + "/" + dirPath + "/"
                    + file.getOriginalFilename());
            //拼装redisKey（sinoydfile:<文件名称>）
            String redisKey = sinoydParseFileRedisKey + file.getOriginalFilename();
            //将文件名称对应的操作人存入redis中（有效期1分钟）
            redisTemplate.opsForValue().set(redisKey, userId, 60, TimeUnit.SECONDS);
            try {
                Files.deleteIfExists(path);
                Files.copy(file.getInputStream(), path);
            } catch (IOException e) {
                e.printStackTrace();
                throw new BaseException("上传失败");
            }
        }
        return true;
    }

    @Override
    public Boolean parse(List<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return false;
        }
        String channel = this.channel;
        String username = PrincipalContextUser.getPrincipal().getUserName();
        String message = ids.parallelStream().collect(Collectors.joining(",")) + ";" + username;
        redisTemplate.convertAndSend(channel, message);
        return true;
    }

    /**
     * 查重方法
     * @param entity 实体
     */
    private void checkExist(DtoFileApp entity) {
        Integer count = repository.countByAppNameAndParseTypeAndIdNot(entity.getAppName(), entity.getParseType(), entity.getId());
        if (StringUtils.isNotNull(count) && count > 0) {
            throw new BaseException("存在同名应用：" + entity.getAppName());
        }
    }

    /**
     * 装配成功、失败目录
     * @param fileApp 解析应用
     */
    private void initFolder(DtoFileApp fileApp) {
        String folder = fileApp.getFolderName();
        folder = suitFolder(folder);
        fileApp.setFolderName(folder);
        if (StringUtils.isNotEmpty(folder)) {
            fileApp.setSucFolderName(SUCCESS_FOLDER_PREFIX + folder);
            fileApp.setFailFolderName(FAILURE_FOLDER_PREFIX + folder);
        }
    }

    /**
     * 去除前后/
     * @param folder 路径
     * @return 路径
     */
    private String suitFolder(String folder) {
        if (StringUtils.isEmpty(folder)) {
            return null;
        }
        if (folder.startsWith("/")) {
            folder = folder.substring(folder.indexOf("/") + 1);
        }
        if (folder.endsWith("/")) {
            folder = folder.substring(0, folder.lastIndexOf("/"));
        }
        return folder;
    }
}