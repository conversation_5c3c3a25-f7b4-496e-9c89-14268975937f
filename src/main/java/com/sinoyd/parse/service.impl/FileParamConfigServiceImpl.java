package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoFileParamConfig;
import com.sinoyd.parse.enums.EnumParamBlockType;
import com.sinoyd.parse.enums.EnumParamDataType;
import com.sinoyd.parse.enums.EnumParamGapBlockType;
import com.sinoyd.parse.repository.FileParamConfigRepository;
import com.sinoyd.parse.service.FileParamConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;


/**
 * FileParamConfig操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class FileParamConfigServiceImpl extends BaseJpaServiceImpl<DtoFileParamConfig,String,FileParamConfigRepository> implements FileParamConfigService {

    @Override
    public void findByPage(PageBean<DtoFileParamConfig> pb, BaseCriteria fileParamConfigCriteria) {
        pb.setEntityName("DtoFileParamConfig a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fileParamConfigCriteria);
        List<DtoFileParamConfig> paramConfigs = pb.getData();
        Map<String, String> dataTypeMap = EnumParamDataType.getMapData();
        Map<String, String> blockTypeMap = EnumParamBlockType.getMapData();
        Map<String, String> gapBlockTypeMap = EnumParamGapBlockType.getMapData();
        for (DtoFileParamConfig paramConfig : paramConfigs) {
            String dataType = paramConfig.getDataType();
            String blockType = paramConfig.getBlockTypeName();
            if (StringUtils.isNotEmpty(dataType)) {
                paramConfig.setDataTypeName(dataTypeMap.getOrDefault(dataType, ""));
            }
            if (StringUtils.isNotEmpty(blockType)) {
                if (EnumParamDataType.GAP_BLOCK.getValue().equals(blockType)) {
                    paramConfig.setBlockTypeName(gapBlockTypeMap.getOrDefault(blockType, ""));
                } else {
                    paramConfig.setBlockTypeName(blockTypeMap.getOrDefault(blockType, ""));
                }
            }
        }
    }

    @Override
    @Transactional
    public DtoFileParamConfig save(DtoFileParamConfig entity) {
        checkExist(entity);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoFileParamConfig update(DtoFileParamConfig entity) {
        checkExist(entity);
        if (!entity.getRegular()) {
            entity.setEigenStart(null);
            entity.setEigenEnd(null);
        }
        String dataType = entity.getDataType();
        if (EnumParamDataType.VALUE.getValue().equals(dataType)
                || EnumParamDataType.GAP_VALUE.getValue().equals(dataType)) {
            entity.setBlockType(null);
            entity.setStartRowIndex(-1);
            entity.setStartRowOffset(-1);
            entity.setStartColumnIndex(-1);
            entity.setStartColumnOffset(-1);
            entity.setEndRowIndex(-1);
            entity.setEndRowOffset(-1);
            entity.setEndColumnIndex(-1);
            entity.setEndColumnOffset(-1);
        } else {
            // 偏移量置0
            entity.setRowOffset(0);
            entity.setColumnOffset(0);
            entity.setRowIndex(-1);
            entity.setColumnIndex(-1);
        }
        return repository.save(entity);
    }

    /**
     * 查重方法
     * @param entity 实体
     */
    private void checkExist(DtoFileParamConfig entity) {
        Integer count = repository.countByParamNameAndFlowIdAndIdNot(entity.getParamName(),
                entity.getFlowId(), entity.getId());
        if (StringUtils.isNotNull(count) && count > 0) {
            throw new BaseException("存在同名参数：" + entity.getParamName());
        }
    }
}