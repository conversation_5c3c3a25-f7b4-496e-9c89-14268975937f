package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoFileResultAlais;
import com.sinoyd.parse.enums.EnumResultType;
import com.sinoyd.parse.repository.FileResultAlaisRepository;
import com.sinoyd.parse.service.FileResultAlaisService;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * FileResultAlais操作接口
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @Service
public class FileResultAlaisServiceImpl extends BaseJpaServiceImpl<DtoFileResultAlais,String,FileResultAlaisRepository> implements FileResultAlaisService {

    @Override
    public void findByPage(PageBean<DtoFileResultAlais> pb, BaseCriteria fileResultAlaisCriteria) {
        pb.setEntityName("DtoFileResultAlais a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, fileResultAlaisCriteria);
        List<DtoFileResultAlais> resultAlaisList = pb.getData();
        Map<String, String> valueTypeMap = EnumResultType.getMapData();
        for (DtoFileResultAlais resultAlais : resultAlaisList) {
            String valueType = resultAlais.getResultType();
            if (StringUtils.isNotEmpty(valueType)) {
                resultAlais.setResultTypeName(valueTypeMap.getOrDefault(valueType, ""));
            }
        }
    }

    @Override
    @Transactional
    public DtoFileResultAlais update(DtoFileResultAlais entity) {
        checkExist(entity);
        return super.update(entity);
    }

    @Override
    @Transactional
    public DtoFileResultAlais save(DtoFileResultAlais entity) {
        checkExist(entity);
        return super.save(entity);
    }

    /**
     * 查重方法
     * @param entity 实体
     */
    private void checkExist(DtoFileResultAlais entity) {
        List<DtoFileResultAlais> resultList = repository.findByPlanIdAndResultTypeAndIdNotAndIsDeletedFalse(entity.getPlanId(), entity.getResultType(), entity.getId());
        if (StringUtils.isNotEmpty(resultList)) {
            resultList = resultList.stream().filter(p -> entity.getParamName().equals(p.getParamName())
                    && entity.getParamAlias().equals(p.getParamAlias())).collect(Collectors.toList());
            if (StringUtils.isNotEmpty(resultList)) {
                throw new BaseException("存在重复结果配置：" + entity.getParamName());
            }
        }
    }
}