package com.sinoyd.parse.service.impl;

import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoDocument;
import com.sinoyd.parse.dto.DtoStreamConfig;
import com.sinoyd.parse.dto.DtoStreamParamConfig;
import com.sinoyd.parse.repository.StreamAppRepository;
import com.sinoyd.parse.repository.StreamConfigRepository;
import com.sinoyd.parse.repository.StreamParamConfigRepository;
import com.sinoyd.parse.service.StreamConfigService;
import com.sinoyd.parse.util.CharUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.sinoyd.parse.service.impl.FileConfigServiceImpl.downFile;


/**
 * 解析流方案配置数据访问操作接口实现类
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class StreamConfigServiceImpl extends BaseJpaServiceImpl<DtoStreamConfig, String, StreamConfigRepository> implements StreamConfigService {

    /*
     * 解析流方案参数信息数据访问操作接口
     */
    @Autowired
    private StreamParamConfigRepository streamParamConfigRepository;

    /*
     * 解析流应用数据访问操作接口
     */
    @Autowired
    private StreamAppRepository streamAppRepository;

    @Value("${fileProps.filePath}")
    private String filePath;

    @Override
    public void findByPage(PageBean<DtoStreamConfig> pb, BaseCriteria streamConfigCriteria) {
        pb.setEntityName("DtoStreamConfig a");
        pb.setSelect("select new com.sinoyd.parse.dto.DtoStreamConfig(a.id, a.planName, a.textSplit, a.batchStream, a.enCode) ");
        this.comRepository.findByPage(pb, streamConfigCriteria);
    }

    @Override
    public DtoStreamConfig findStreamConfig(String id) {
        DtoStreamConfig dtoStreamConfig = super.findOne(id);
        if (StringUtils.isNull(dtoStreamConfig) || dtoStreamConfig.getIsDeleted()) {
            throw new BaseException("解析流方案配置信息不存在!");
        }
        return dtoStreamConfig;
    }

    @Override
    public List<DtoStreamParamConfig> findParamConfigByPlanId(String planId) {
        DtoStreamConfig dtoStreamConfig = super.findOne(planId);
        if (StringUtils.isNull(dtoStreamConfig) || dtoStreamConfig.getIsDeleted()) {
            throw new BaseException("解析流方案不存在或已删除!");
        }
        List<DtoStreamParamConfig> dtoStreamParamConfigs;
        dtoStreamParamConfigs = streamParamConfigRepository.findByPlanId(planId);
        return dtoStreamParamConfigs;
    }

    @Override
    @Transactional
    public DtoStreamConfig saveStreamConfig(DtoStreamConfig entity) {
        if (StringUtils.isNull(entity) || StringUtils.isEmpty(entity.getPlanName())) {
            throw new BaseException("解析方案名称不能为空!");
        }
        //若名称长度超出限制则直接抛出异常
        if (entity.getPlanName().length() > 25 && CharUtil.calByteLen(entity.getPlanName(), 2, 1) > 50) {
            throw new BaseException("方案名称长度超出限制,不能新增！");
        }
        List<DtoStreamConfig> oriStreamConfigList = repository.findByPlanNameAndIsDeleted(entity.getPlanName(), false);
        if (StringUtils.isNotEmpty(oriStreamConfigList)) {
            throw new BaseException("存在同名方案!");
        }
        entity.setIsDeleted(false);
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoStreamConfig updateStreamConfig(DtoStreamConfig entity) {
        if (StringUtils.isNull(entity) || StringUtils.isEmpty(entity.getPlanName())) {
            throw new BaseException("解析方案名称不能为空!");
        }
        //若名称长度超出限制则直接抛出异常
        if (entity.getPlanName().length() > 25 && CharUtil.calByteLen(entity.getPlanName(), 2, 1) > 50) {
            throw new BaseException("方案名称长度超出限制,不能修改！");
        }
        String id = entity.getId();
        if (StringUtils.isEmpty(id)) {
            throw new BaseException("解析方案id不能为空!");
        }
        DtoStreamConfig oriStreamConfig = super.findOne(id);
        if (StringUtils.isNull(oriStreamConfig) || oriStreamConfig.getIsDeleted()) {
            throw new BaseException("该解析方案已被删除!");
        }
        //若修改了方案名称则需要判断是否存在同名方案
        if (!entity.getPlanName().equals(oriStreamConfig.getPlanName())) {
            List<DtoStreamConfig> streamConfigList = repository.findByPlanNameAndIsDeleted(entity.getPlanName(), false);
            if (StringUtils.isNotEmpty(streamConfigList)) {
                throw new BaseException("解析流方案名称已存在!");
            }
        }
        entity.setIsDeleted(false);
        entity.setCreateDate(oriStreamConfig.getCreateDate());
        entity.setCreator(oriStreamConfig.getCreator());
        return repository.save(entity);
    }

    @Override
    @Transactional
    public Integer deleteByIds(List<String> ids) {
        if (StringUtils.isEmpty(ids)) {
            return 0;
        }
        //逻辑删除对应的参数配置信息
        streamParamConfigRepository.logicDeleteByPlanId(ids, true);
        //逻辑删除对应的解析流应用
        streamAppRepository.logicDeleteByPlanId(ids, true);
        return super.logicDeleteById(ids);
    }

    @Transactional
    @Override
    public String upload(HttpServletRequest request, String configId) {
        DtoStreamConfig streamConfig = super.findOne(configId);
        if (StringUtils.isNull(streamConfig)) {
            throw new BaseException("方案不存在!");
        }
        MultipartFile multipartFile = ((MultipartHttpServletRequest) request).getFile("file");
        String newPath = "/streamConfigAttach";
        //创建文件目录
        String checkPath = filePath + newPath;
        File fileStream = new File(checkPath);
        if (!fileStream.exists()) {
            fileStream.mkdirs();
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        //后缀名
        String fileName = multipartFile.getOriginalFilename();
        //物理名称
        String physicalName = dateFormat.format(new Date()) + "_" + fileName;

        File file = new File(new File(checkPath).getAbsoluteFile() + File.separator + physicalName);
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        try {
            multipartFile.transferTo(file);
            streamConfig.setPlanFile(newPath + "/" + physicalName);
            repository.save(streamConfig);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return fileName;
    }

    @Override
    public String download(String configId, HttpServletResponse response) {
        DtoStreamConfig streamConfig = super.findOne(configId);
        if (StringUtils.isNull(streamConfig)) {
            throw new BaseException("方案不存在!");
        }
        String path = filePath + streamConfig.getPlanFile();
        String fileName = path.substring(path.lastIndexOf("/") + 1, path.length());
        File file = new File(path);
        if (!file.exists()) {
            throw new BaseException("文件不存在,请确认");
        }
        downFile(file, response, fileName);
        return fileName;
    }

    @Override
    public List<DtoDocument> getAttachInfo(String configId) {
        List<DtoDocument> dtoDocumentList = new ArrayList<>();
        DtoStreamConfig streamConfig = super.findOne(configId);
        if (StringUtils.isNull(streamConfig)) {
            throw new BaseException("方案不存在!");
        }
        String filePlan = streamConfig.getPlanFile();
        if (StringUtils.isEmpty(filePlan)) {
            return dtoDocumentList;
        }
        int lineIdx = filePlan.indexOf("_");
        int dotIndex = filePlan.lastIndexOf(".");
        String fileName = filePlan.substring(lineIdx + 1, dotIndex);
        String fileType = filePlan.substring(dotIndex);
        DtoDocument dtoDocument = new DtoDocument();
        dtoDocument.setConfigId(configId);
        dtoDocument.setFileName(fileName);
        dtoDocument.setFileType(fileType);
        dtoDocument.setFilePath(filePlan);
        dtoDocumentList.add(dtoDocument);
        return dtoDocumentList;
    }

    @Transactional
    @Override
    public void deleteAttachInfo(String configId) {
        DtoStreamConfig streamConfig = super.findOne(configId);
        if (StringUtils.isNull(streamConfig)) {
            throw new BaseException("方案不存在!");
        }
        streamConfig.setPlanFile("");
        repository.save(streamConfig);
    }
}