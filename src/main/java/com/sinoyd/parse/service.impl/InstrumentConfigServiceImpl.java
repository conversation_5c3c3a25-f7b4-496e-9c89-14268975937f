package com.sinoyd.parse.service.impl;

import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.DtoInstrumentConfig;
import com.sinoyd.parse.repository.InstrumentConfigRepository;
import com.sinoyd.parse.service.InstrumentConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;


/**
 * 解析流仪器配置操作实现
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@Service
public class InstrumentConfigServiceImpl extends BaseJpaServiceImpl<DtoInstrumentConfig, String, InstrumentConfigRepository>
        implements InstrumentConfigService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer logicDeleteById(Collection<String> ids) {
        return repository.logicDeleteById(ids, new Date());
    }
}
