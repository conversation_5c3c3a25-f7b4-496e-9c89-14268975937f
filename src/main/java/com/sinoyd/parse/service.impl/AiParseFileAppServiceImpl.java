package com.sinoyd.parse.service.impl;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.sinoyd.boot.common.exception.BaseException;
import com.sinoyd.boot.common.util.StringUtils;
import com.sinoyd.frame.base.criteria.BaseCriteria;
import com.sinoyd.boot.common.util.DateUtil;
import com.sinoyd.frame.base.util.PageBean;
import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
import com.sinoyd.parse.dto.*;
import com.sinoyd.parse.entity.Datas;
import com.sinoyd.parse.enums.*;
import com.sinoyd.parse.repository.*;
import com.sinoyd.parse.service.AiParseDataHandleService;
import com.sinoyd.parse.service.AiParseFileAppService;
import com.sinoyd.parse.vo.*;
import com.sinoyd.parse.websocket.AiInstrumentParseWSServer;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * AI仪器解析应用操作接口实现类
 *
 * <AUTHOR>
 * @version V6.0.0
 * @since 2025/08/13
 **/
@Slf4j
@Service
public class AiParseFileAppServiceImpl extends BaseJpaServiceImpl<DtoAiParseFileApp, String, AiParseFileAppRepository> implements AiParseFileAppService {

    private AiInstrumentConfigRepository aiInstrumentConfigRepository;

    private ParseDocumentRepository parseDocumentRepository;

    private AiParseDataHandleService aiParseDataHandleService;

    private DatasRepository datasRepository;

    private ParseLogRepository parseLogRepository;

    private AppFlowDataRepository appFlowDataRepository;

    /**
     * AI/OCR仪器解析请求路径
     */
    @Value("${ai-parse.url:http://*************:19990/api/call-agent}")
    private String ocrUrl;

    /**
     * AI/OCR仪器解析同步批量解析数量（默认6）
     */
    @Value("${ai-parse.sync-batch-count:6}")
    private Integer syncBatchCount;

    /**
     * 系统文件根路径
     */
    @Value("${fileProps.filePath}")
    private String filePath;


    @Override
    public void findByPage(PageBean<DtoAiParseFileApp> pb, BaseCriteria aiParseFileAppCriteria) {
        pb.setEntityName("DtoAiParseFileApp a");
        pb.setSelect("select a");
        comRepository.findByPage(pb, aiParseFileAppCriteria);

        // 设置解析方式名称和解析状态名称
        List<DtoAiParseFileApp> appList = pb.getData();
        loadTransientFields(appList);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp save(DtoAiParseFileApp entity) {
        //设置解析状态为未解析
        entity.setParseStatus(EnumAIParseStatus.UN_PARS.getValue());
        return super.save(entity);
    }

    @Override
    @Transactional
    public DtoAiParseFileApp update(DtoAiParseFileApp entity) {
        return super.update(entity);
    }

    @Override
    public DtoAiParseFileApp findOne(String key) {
        DtoAiParseFileApp fileApp = super.findOne(key);
        loadTransientFields(Collections.singletonList(fileApp));
        return fileApp;
    }

    @Override
    public void batchParse(AiParseBatchVO parseBatchVO) {
        if (StringUtils.isEmpty(parseBatchVO.getAppIds())) {
            return;
        }
        //查询所选的AI解析应用
        List<DtoAiParseFileApp> appList = repository.findAllById(parseBatchVO.getAppIds());
        loadTransientFields(appList);
        //如果存在不是未解析状态的数据，则抛出异常
        if (appList.stream().anyMatch(p -> !EnumAIParseStatus.UN_PARS.getValue().equals(p.getParseStatus()))) {
            throw new BaseException("存在已解析的应用，请重新选择！");
        }

        //AI解析应用Map
        Map<String, DtoAiParseFileApp> appMap = appList.stream().collect(Collectors.toMap(DtoAiParseFileApp::getId, dto -> dto));
        //判断解析应用中是否存在未上传附件的应用，如果存在，则抛出异常提醒
        if (appList.stream().anyMatch(p -> StringUtils.isEmpty(p.getDocumentPath()))) {
            throw new BaseException("存在未上传附件的应用，请先上传附件再进行解析！");
        }
        //SessionId
        String sessionId = parseBatchVO.getSessionId();
        //获取主线程上下文
        SecurityContext context = SecurityContextHolder.getContext();
        // 在新线程中执行，避免阻塞主线程
        new Thread(() -> {
            // 固定线程数并发执行
            ExecutorService executor = Executors.newFixedThreadPool(syncBatchCount);
            List<CompletableFuture<Void>> tasks = new java.util.ArrayList<>();

            //设置线程上下文
            SecurityContextHolder.setContext(context);

            for (String appId : parseBatchVO.getAppIds()) {
                DtoAiParseFileApp app = appMap.get(appId);
                CompletableFuture<Void> task = CompletableFuture.runAsync(() -> {
                    //设置线程上下文
                    SecurityContextHolder.setContext(context);
                    //执行AI解析请求
                    sendOcrRequestMsg(parseBatchVO, app, new OcrRequestVO(ocrUrl, app.getPromptText(), app.getDocumentPath()));
                    //清理上下文
                    SecurityContextHolder.clearContext();
                }, executor);
                tasks.add(task);
            }
            boolean isSuccess = true;
            try {
                CompletableFuture.allOf(tasks.toArray(new CompletableFuture[0])).join();
            } catch (Exception e) {
                log.error("合并解析结果失败：", e);
                isSuccess = false;
                AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "合并解析结果失败", EnumMessageType.JSON);
            } finally {
                //执行批量Redis数据保存（除解析结果的数据）
                aiParseDataHandleService.batchSaveRedisDB(parseBatchVO.getAppIds());
                //关闭线程池
                executor.shutdown();
                //清理上下文
                SecurityContextHolder.clearContext();
                //放在最后进行解析完成的消息发送，防止发送完成页面刷新时，状态还未保存
                if (isSuccess){
                    //向WebSocket发布全部解析完成消息
                    AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, "全部解析完成", EnumMessageType.JSON);
                }
            }
        }).start();
    }

    @Override
    public List<AiParseParamDataVO> findAppParseData(String appId) {
        //查询
        //从Redis中获取数据
        List<AiParseParamDataVO> parseParmDataVO = aiParseDataHandleService.findAppParseDataRedisByAppId(appId);
        //如果Redis中没有数据则查询数据库
        if (StringUtils.isEmpty(parseParmDataVO)) {
            Optional<DtoLog> logOp = parseLogRepository.findByAppId(appId).stream().findFirst();
            if (logOp.isPresent()) {
                List<DtoDatas> dataList = datasRepository.findByParselogId(logOp.get().getId());
                dataList.forEach(
                        p -> parseParmDataVO.add(new AiParseParamDataVO(p)));
            }
        }
        return parseParmDataVO;
    }

    @Override
    public List<DtoAppFlowData> findAppFlowData(String appId) {
        //从Redis中获取数据
        List<DtoAppFlowData> flowDataList = aiParseDataHandleService.findAppFlowDataRedisByAppId(appId);
        //如果Redis中没有数据则查询数据库
        if (StringUtils.isEmpty(flowDataList)) {
            Optional<DtoLog> logOp = parseLogRepository.findByAppId(appId).stream().findFirst();
            if (logOp.isPresent()) {
                flowDataList = appFlowDataRepository.findByParselogId(logOp.get().getId());
            }
        }
        return flowDataList;
    }

    @Override
    public DtoAiParseFileApp findAttachPath(String id) {
        return findOne(id);
    }

    @Override
    @Transactional
    public Integer logicDeleteById(Collection<String> strings) {
        aiParseDataHandleService.deleteRedis(strings);
        return super.logicDeleteById(strings);
    }

    @Override
    @Transactional
    public List<DtoDatas> saveDataList(String appId, List<AiParseParamDataVO> parseParamsData) {
        List<DtoDatas> dataList = new ArrayList<>();
        //查询应用相关数据
        DtoAiParseFileApp fileApp = findOne(appId);
        loadTransientFields(Collections.singletonList(fileApp));
        List<String> dataIds = parseParamsData.stream().map(AiParseParamDataVO::getId).collect(Collectors.toList());
        Map<String, DtoDatas> dataMap = datasRepository.findAllById(dataIds).stream().collect(Collectors.toMap(Datas::getId, d -> d));
        //应用数据标记为已保存
        fileApp.setIsSaved(true);
        //处理参数数据
        parseParamsData.forEach(p -> {
            DtoDatas data = dataMap.getOrDefault(p.getId(), new DtoDatas());
            data.setId(p.getId());
            data.setAppId(appId);
            data.setInstrumentCode(fileApp.getInstrumentCode());
            data.setInstrumentId(fileApp.getInstrumentId());
            data.setAnalyzeItemName(fileApp.getInstrumentName());
            data.setParselogId(p.getParseLogId());
            data.setInstrumentCode(p.getInstrumentCode());
            data.setSampleCode(p.getGatherCode());
            data.setAnalyzeItemName(p.getAnalyzeItem());
            data.setParamName(p.getParamName());
            data.setValue(p.getSaveValue());
            data.setParseDataTime(DateUtil.stringToDate(p.getParseDateTime(), DateUtil.FULL));
            //预留值1存储量纲单位
            data.setExtend1(p.getUnit());
            dataList.add(data);
        });
        if (StringUtils.isNotEmpty(dataList)) {
            List<DtoDatas> save = datasRepository.saveAll(dataList);
            aiParseDataHandleService.deleteRedisByKey(Collections.singletonList(appId), AiParseDataHandleServiceImpl.AI_PARSE_DATA_REDIS_KEY);
            return save;
        }
        return new ArrayList<>();
    }

    /**
     * 清理JSON字符串，去除不必要的转义
     *
     * @param jsonStr 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    private String cleanJsonString(String jsonStr) {
        if (StringUtils.isEmpty(jsonStr)) {
            return jsonStr;
        }

        try {
            // 先解析为Object，再重新序列化，这样可以自动去除不必要的转义
            Object obj = JSONObject.parse(jsonStr);
            return JSONObject.toJSONString(obj, SerializerFeature.DisableCircularReferenceDetect);
        } catch (Exception e) {
            log.warn("JSON字符串清理失败，返回原字符串: {}", e.getMessage());
            return jsonStr;
        }
    }


    /**
     * AI解析请求（向WebSocket发送步骤消息）
     *
     * @param parseBatchVO AI解析批量请求参数
     * @param parseApp     AI解析应用
     * @param requestVO    AI解析图片文件路径
     */
    public void sendOcrRequestMsg(AiParseBatchVO parseBatchVO, DtoAiParseFileApp parseApp, OcrRequestVO requestVO) {
        String appId = parseApp.getId();
        //保存日志Redis数据
        DtoLog parseLog = aiParseDataHandleService.saveLogRedis(parseApp);
        //步骤计数器
        AtomicInteger stepCount = new AtomicInteger(0);
        //步骤名称
        AtomicReference<String> stepName = new AtomicReference<>();
        //执行AI解析请求
        try (BufferedReader reader = doStreamRequest(requestVO, parseApp)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty() && !line.contains("id:")) {
                    String json = cleanJsonString(line.replace("data: ", ""));
                    JSONObject dataJsObj = JSONObject.parseObject(json);
                    //获取步骤名称
                    stepName.set(dataJsObj.getString("step_name"));
                    //获取当前步骤相关数据
                    AiParseStepVO stepVO = new AiParseStepVO(dataJsObj, appId);
                    if (StringUtils.isNull(stepVO.getStepId())) {
                        stepVO.setStepId(String.valueOf(stepCount.get() + 1));
                    }
                    stepCount.set(Integer.parseInt(stepVO.getStepId()));
                    //如果Ocr解析步骤完成，需要进行LIMS数据转义进行最后步骤展示
                    if (stepVO.getIsOcrFinish()) {
                        stepVO.setStepName(EnumAIParseStep.OCR_FINISH.getName());
                        stepName.set(EnumAIParseStep.OCR_FINISH.getName());
                        //解析数据转换，存入redis中
                        List<AiParseParamDataVO> parseDataList = aiParseDataHandleService.saveParseDataRedis(appId, parseLog.getId(), dataJsObj);

                        //如果当前解析的应用为当前选中应用，则需要发布解析步骤数据
                        AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, stepVO, EnumMessageType.JSON);
                        //创建解析完成消息
                        AiParseStepVO stepFinish = new AiParseStepVO(String.valueOf(Integer.parseInt(stepVO.getStepId()) + 1), EnumAIParseStep.LIMS_DATA_PARSE.getName(), true, appId, parseDataList);
                        stepFinish.setIsOcrFinish(true);
                        AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, stepFinish, EnumMessageType.JSON);
                        //保存lims数据解析步骤
                        aiParseDataHandleService.saveStepLogRedis(appId, parseLog.getId(), stepFinish);
                    } else {
                        //如果当前解析的应用为当前选中应用，则需要发布解析步骤数据
                        AiInstrumentParseWSServer.send(parseBatchVO.getSessionId(), EnumWebSocketType.AI_PARSE_REALTIME, stepVO, EnumMessageType.JSON);
                    }
                    //将AI步骤数据保存到解析日志中（先存入缓存，当全部数据解析完成之后，持久化到数据库中）
                    aiParseDataHandleService.saveStepLogRedis(appId, parseLog.getId(), stepVO);

                    // 添加小延迟，确保数据能够及时发送
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        } catch (IOException e) {
            log.error("AI解析接口请求失败：", e);
            //创建解析异常消息并保存异常步骤日志
            sendAndSaveFailStepLog(parseBatchVO.getSessionId(), appId, stepName, stepCount, parseLog, "AI解析接口请求异常", "AI解析接口请求失败;" + e.getMessage());
        } catch (JSONException e) {
            log.error("AI解析接口数据转换失败：", e);
            //创建解析异常消息并保存异常步骤日志
            sendAndSaveFailStepLog(parseBatchVO.getSessionId(), appId, stepName, stepCount, parseLog, "AI解析数据转换异常", "AI解析接口数据转换失败;" + e.getMessage());
        } catch (Exception e) {
            log.error("AI解析程序异常：", e);
            //创建解析异常消息并保存异常步骤日志
            sendAndSaveFailStepLog(parseBatchVO.getSessionId(), appId, stepName, stepCount, parseLog, "AI解析程序异常", "AI解析程序异常;" + e.getMessage());
        }
    }

    /**
     * 创建解析异常消息并保存异常步骤日志
     *
     * @param sessionId WebSocket会话id
     * @param appId     AI解析应用id
     * @param stepName  AI解析步骤名称
     * @param stepCount AI解析步骤计数器
     * @param parseLog  AI解析日志
     * @param errorType 错误类型
     * @param errorMsg  错误消息
     */
    private void sendAndSaveFailStepLog(String sessionId, String appId, AtomicReference<String> stepName, AtomicInteger stepCount, DtoLog parseLog, String errorType, String errorMsg) {
        String stepNameValue = StringUtils.isNotEmpty(stepName.get()) ? stepName.get() : EnumAIParseStep.AI_PARSE_FAIL.getName();

        Integer stepCountValue = StringUtils.isNotEmpty(stepName.get()) ? stepCount.get() : stepCount.get() + 1;
        //创建解析异常消息
        AiParseStepVO stepFail = new AiParseStepVO(String.valueOf(stepCountValue), stepNameValue, false, appId, errorMsg);
        stepFail.setIsError(true);
        stepFail.setErrorType(errorType);
        AiInstrumentParseWSServer.send(sessionId, EnumWebSocketType.AI_PARSE_REALTIME, stepFail, EnumMessageType.JSON);
        //将AI步骤数据保存到解析日志中（先存入缓存，当全部数据解析完成之后，持久化到数据库中）
        aiParseDataHandleService.saveStepLogRedis(appId, parseLog.getId(), stepFail);
    }

    /**
     * OCR_AI解析请求
     *
     * @param requestVO AI解析请求参数
     * @return 流式接口结果
     */
    private BufferedReader doStreamRequest(OcrRequestVO requestVO, DtoAiParseFileApp parseApp) throws IOException {
        requestVO.fillTypeUrl(parseApp.getParseType());
        String requestUrl = requestVO.getUrl() + requestVO.getUrlSuffix();
        File file = new File(filePath + requestVO.getFilePath());
        RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("prompt", requestVO.getPrompt()).addFormDataPart(requestVO.getFormDataParam(), file.getName(), RequestBody.create(MediaType.parse("application/octet-stream"), file)).build();
        Request request = new Request.Builder().url(requestUrl).post(body).build();
        OkHttpClient client = new OkHttpClient.Builder().connectTimeout(60, TimeUnit.SECONDS).readTimeout(180, TimeUnit.SECONDS).build();
        Response response = client.newCall(request).execute();
        if (!response.isSuccessful()) {
            throw new BaseException("OCR解析接口请求失败: " + response.code());
        }
        ResponseBody responseBody = response.body();
        assert responseBody != null;
        return new BufferedReader(new InputStreamReader(responseBody.byteStream(), StandardCharsets.UTF_8));
    }


    /**
     * 加载冗余字段
     *
     * @param data 应用数据
     */
    private void loadTransientFields(Collection<DtoAiParseFileApp> data) {

        //查询附件数据
        List<String> appIds = data.stream().map(DtoAiParseFileApp::getId).collect(Collectors.toList());
        List<DtoParseDocument> documentList = parseDocumentRepository.findByObjectIdInAndDocTypeId(appIds, EnumDocType.AI_PARSE_APP_FILE.getDocTypeId());
        //按照ObjectId分组附件数据
        Map<String, List<DtoParseDocument>> documentMap = documentList.stream().collect(Collectors.groupingBy(DtoParseDocument::getObjectId));
        //相关枚举Map
        Map<String, String> parseTypeMap = EnumParseType.getMapData();
        Map<String, String> parseStatusMap = EnumAIParseStatus.getMapData();

        //查询关联AI仪器解析配置
        List<String> instrumentIds = data.stream().map(DtoAiParseFileApp::getInstrumentId).collect(Collectors.toList());
        List<DtoAiInstrumentConfig> instrumentConfigList = aiInstrumentConfigRepository.findAllById(instrumentIds);
        Map<String, DtoAiInstrumentConfig> instrumentConfigMap = instrumentConfigList.stream().collect(Collectors.toMap(DtoAiInstrumentConfig::getId, dto -> dto));

        //遍历应用数据
        for (DtoAiParseFileApp app : data) {
            //获取应用下的最新的附件
            List<DtoParseDocument> documents = documentMap.getOrDefault(app.getId(), null);
            if (documents != null && !documents.isEmpty()) {
                Optional<DtoParseDocument> docOp = documents.stream().max(Comparator.comparing(DtoParseDocument::getCreateDate));
                docOp.ifPresent(p -> {
                    app.setDocument(p);
                    app.setDocumentId(p.getId());
                    app.setDocumentPath(p.getPath());
                });

            }
            // 设置解析方式名称
            if (app.getParseType() != null) {
                String parseTypeName = parseTypeMap.getOrDefault(app.getParseType(), "");
                app.setParseTypeName(parseTypeName);
            }

            // 设置解析状态名称
            if (app.getParseStatus() != null) {
                String parseStatusName = parseStatusMap.getOrDefault(app.getParseStatus(), "");
                app.setParseStatusName(parseStatusName);
            }

            //设置配置的提示词
            DtoAiInstrumentConfig instrumentConfig = instrumentConfigMap.getOrDefault(app.getInstrumentId(), null);
            if (instrumentConfig != null) {
                app.setPromptText(instrumentConfig.getPromptText());
            }
        }
    }

    @Autowired
    public void setAiInstrumentConfigRepository(AiInstrumentConfigRepository aiInstrumentConfigRepository) {
        this.aiInstrumentConfigRepository = aiInstrumentConfigRepository;
    }

    @Autowired
    public void setParseDocumentRepository(ParseDocumentRepository parseDocumentRepository) {
        this.parseDocumentRepository = parseDocumentRepository;
    }

    @Autowired
    public void setAiParseDataHandleService(AiParseDataHandleService aiParseDataHandleService) {
        this.aiParseDataHandleService = aiParseDataHandleService;
    }

    @Autowired
    public void setDatasRepository(DatasRepository datasRepository) {
        this.datasRepository = datasRepository;
    }

    @Autowired
    public void setParseLogRepository(ParseLogRepository parseLogRepository) {
        this.parseLogRepository = parseLogRepository;
    }

    @Autowired
    public void setAppFlowDataRepository(AppFlowDataRepository appFlowDataRepository) {
        this.appFlowDataRepository = appFlowDataRepository;
    }
}
