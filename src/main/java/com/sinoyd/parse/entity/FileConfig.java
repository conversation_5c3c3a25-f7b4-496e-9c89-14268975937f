package com.sinoyd.parse.entity;


import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * FileConfig实体
 *
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class FileConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public FileConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();

    /**
     * 方案名
     */
    @Column(length = 500)
    @Length(max = 500, message = "方案名长度不能超过500")
    private String planName;

    /**
     * 文件类型(1:txt;2:excel;3:word;4:pdf 5:mdb)
     */
    @Column(length = 25)
    private String fileType;

    /**
     * 空时为默认编码格式
     */
    @Column(length = 25)
    @Length(max = 25, message = "空时为默认编码格式长度不能超过25")
    private String fileCode;

    /**
     * 处理方式(解析,上传)
     */
    @Column(length = 25)
    private String handleType;

    /**
     * 方案附件路径
     */
    @Column
    private String planFile;

    /**
     * 组织机构Id
     */
    @Column(length = 25)
    private String orgId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 25)
    @CreatedBy
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length = 25)
    @LastModifiedBy
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    private Date modifyDate;

}