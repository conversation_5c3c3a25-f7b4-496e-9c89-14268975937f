package com.sinoyd.parse.entity;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * FileApp实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FileApp implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  FileApp() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 应用名称
    */
    @Column(length=500)
    @Length(max = 500, message = "应用名称长度不能超过500")
	private String appName;
    
    /**
    * 仪器Id
    */
    @Column(length=25)
	private String instrumentId;
    
    /**
    * 仪器名称
    */
    @Column(length=125)
    @Length(max = 125, message = "仪器名称长度不能超过125")
	private String instrumentName;
    
    /**
    * 仪器编号
    */
    @Column(length=125)
    @Length(max = 125, message = "仪器编号长度不能超过125")
	private String instrumentCode;
    
    /**
    * 解析类型（1：文件解析 2：数据流解析 3:文件解析调试）
    */
    @Column(length=25)
	private String parseType;
    
    /**
    * 方案Id(文件解析时必填)
    */
    @Column(length=25)
	private String planId;
    
    /**
    * 监听文件夹（文件夹名称，文件解析时必填）
    */
    @Column(length=125)
    @Length(max = 125, message = "监听文件夹名称长度不能超过125")
	private String folderName;
    
    /**
    * 解析成功存放目录
    */
    @Column(length=125)
    @Length(max = 125, message = "解析成功存放目录长度不能超过125")
	private String sucFolderName;
    
    /**
    * 解析失败存放目录
    */
    @Column(length=125)
    @Length(max = 125, message = "解析失败存放目录长度不能超过125")
	private String failFolderName;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
	private Date modifyDate;
    
 }