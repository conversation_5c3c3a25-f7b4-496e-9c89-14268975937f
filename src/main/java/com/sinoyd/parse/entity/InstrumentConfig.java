package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 仪器配置实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class InstrumentConfig implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public InstrumentConfig() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.newId();

    /**
     * 方案名
     */
    @Column
    private String name;

    /**
     * 蓝牙服务名(飞鹰速传设备名 蓝牙名 英文 32bytes以内)
     */
    @Column
    private String bleServerName;

    /**
     * 是否输出日志
     */
    private Boolean logOut;

    /**
     * 解析类型（0：文本图片 1：文本 2：爱华声级计 5688  3：爱华声级计 6228）
     */
    private Integer analysisType;

    /**
     * 硬件流控
     */
    private Boolean fluidControl;


    /**
     * 应答
     */
    private Boolean response;

    /**
     * 应答周期(ms)
     */
    private Integer responsePeriod;

    /**
     * RS232串口(是否交叉 0：直连 1:交叉  )
     */
    @Column(name = "[cross]")
    private Boolean rsCross;

    /**
     * 波特率
     */
    private Integer baudRate;

    /**
     * USB串口波特率
     */
    private Integer usbBaudRate;

    /**
     *Pin码
     */
    @Column
    private String pin;

    /**
     * 组织机构Id
     */
    @Column
    private String orgId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    private Date modifyDate;


    public InstrumentConfig(String name, String bleServerName, Boolean logOut, Integer analysisType, Boolean fluidControl,
                            Boolean response, Integer responsePeriod, Boolean rsCross, Integer baudRate, Integer usbBaudRate, String pin) {
        this.name = name;
        this.bleServerName = bleServerName;
        this.logOut = logOut;
        this.analysisType = analysisType;
        this.fluidControl = fluidControl;
        this.response = response;
        this.responsePeriod = responsePeriod;
        this.rsCross = rsCross;
        this.baudRate = baudRate;
        this.usbBaudRate = usbBaudRate;
        this.pin = pin;
    }
}