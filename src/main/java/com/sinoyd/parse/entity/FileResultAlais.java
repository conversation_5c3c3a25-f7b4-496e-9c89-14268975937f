package com.sinoyd.parse.entity;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * FileResultAlais实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FileResultAlais implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  FileResultAlais() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 方案Id
    */
    @Column(length=25)
	private String planId;
    
    /**
    * 结果原始名
    */
    @Column(length=500)
    @Length(max = 500, message = "结果原始名长度不能超过500")
	private String paramName;
    
    /**
    * 结果映射名
    */
    @Column(length=500)
    @Length(max = 500, message = "结果映射名长度不能超过500")
	private String paramAlias;
    
    /**
    * 结果类型(1:分析项目名称，2:参数)
    */
    @Column(length=25)
	private String resultType;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
	private Date modifyDate;
    
 }