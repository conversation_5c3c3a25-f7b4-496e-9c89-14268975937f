package com.sinoyd.parse.entity;


import java.io.Serializable;

import com.sinoyd.frame.base.entity.BaseEntity;

import javax.persistence.*;

import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;

import java.util.Date;

import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.validation.constraints.NotNull;

/**
 * AI仪器解析应用实体
 *
 * <AUTHOR>
 * @version V5.2.0
 * @since 2025/08/13
 **/
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class AiParseFileApp implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public AiParseFileApp() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();

    /**
     * AI仪器解析应用名称
     */
    @Column(length = 250)
    @Length(max = 250, message = "应用名称长度不能超过250")
    private String appName;

    /**
     * 仪器id(AI仪器解析配置id)
     */
    @Column(length = 50)
    @Length(max = 50, message = "仪器id长度不能超过50")
    private String instrumentId;

    /**
     * 仪器解析名称
     */
    @Column(length = 250)
    @Length(max = 250, message = "仪器解析名称长度不能超过250")
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column(length = 100)
    @Length(max = 100, message = "仪器编号长度不能超过100")
    private String instrumentCode;

    /**
     * 解析方式（枚举管理：{@link com.sinoyd.parse.enums.EnumAIParseType}）
     */
    @Column
    @NotNull(message = "解析方式不能为空")
    private String parseType;

    /**
     * 解析状态（枚举管理：{@link com.sinoyd.parse.enums.EnumAIParseStatus}）
     */
    @Column
    @NotNull(message = "解析状态不能为空")
    private String parseStatus;

    /**
     * 组织机构Id
     */
    @Column(length = 50)
    @Length(max = 50, message = "组织机构Id长度不能超过50")
    private String orgId;

    /**
     * 解析结果是否已经保存
     */
    private Boolean isSaved = false;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column(length = 50)
    @CreatedBy
    private String creator;

    /**
     * 创建时间
     */
    @CreatedDate
    private Date createDate;

    /**
     * 更新人
     */
    @Column(length = 50)
    @LastModifiedBy
    private String modifier;

    /**
     * 更新时间
     */
    @LastModifiedDate
    private Date modifyDate;
}
