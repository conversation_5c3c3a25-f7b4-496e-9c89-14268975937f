package com.sinoyd.parse.entity;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import org.hibernate.annotations.ColumnDefault;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import java.math.BigDecimal;


/**
 * FileParamConfig实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class FileParamConfig implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  FileParamConfig() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 流程Id
    */
    @Column(length=25)
	private String flowId;
    
    /**
    * 参数名称
    */
    @Column(length=25)
    @Length(max = 25, message = "参数名称长度不能超过25")
	private String paramName;
    
    /**
    * 参数对应的别名
    */
    @Column(length=25)
    @Length(max = 25, message = "参数对应的别名长度不能超过25")
	private String paramAlias;
    
    /**
    * 数据类型(1:值类型  2:间隔值类型 3:块类型 4:间隔块类型)
    */
    @Column(length=25)
	private String dataType;
    
    /**
    * 表名
    */
    @Column(length=125)
    @Length(max = 125, message = "表名长度不能超过125")
	private String tableName;
    
    /**
    * 表格的序号
    */
	private Integer tableIndex;
    
    /**
    * 开始位置
    */
	private Integer startPosition;
    
    /**
    * 截取的长度
    */
	private Integer splitLength;
    
    /**
    * 行--值解析
    */
	private Integer rowIndex;

    /**
     * 行--值解析
     */
    private Integer rowOffset;
    
    /**
    * 列--值解析
    */
	private Integer columnIndex;

    /**
     * 列--值解析
     */
    private Integer columnOffset;
    
    /**
    * 块类型（1：确定列 2：确定行 ）
    */
    @Column(length=25)
	private String blockType;
    
    /**
    * 开始行索引--块解析
    */
	private Integer startRowIndex;
    
    /**
    * 结束行索引--块解析
    */
	private Integer endRowIndex;
    
    /**
    * 开始行偏移量--块解析
    */
	private Integer startRowOffset;
    
    /**
    * 结束行偏移量--块解析
    */
	private Integer endRowOffset;
    
    /**
    * 开始列索引--块解析
    */
	private Integer startColumnIndex;
    
    /**
    * 结束列索引--块解析
    */
	private Integer endColumnIndex;
    
    /**
    * 开始列偏移量--块解析
    */
	private Integer startColumnOffset;
    
    /**
    * 结束列偏移量--块解析
    */
	private Integer endColumnOffset;
    
    /**
    * 是否正则
    */
	private Boolean regular;
    
    /**
    * 正则表达式（开始位置）
    */
    @Column(length=4000)
    @Length(max = 4000, message = "正则表达式（开始位置）长度不能超过4000")
	private String eigenStart;

    /**
    * 正则表达式（结束位置）
    */
    @Column(length=4000)
    @Length(max = 4000, message = "正则表达式（结束位置）长度不能超过4000")
	private String eigenEnd;

    /**
     * 正则表达式（开始位置）
     */
    @Column(length=4000)
    @Length(max = 4000, message = "正则表达式（分隔）长度不能超过4000")
    private String eigenSplit;

    /**
    * 排序号
    */
    @Column(nullable=false)
	private Integer orderNum;
    
    /**
    * 是否倍率转换
    */
	private Boolean rateConvert;
    
    /**
    * 倍率
    */
	private BigDecimal rateNum;

    /**
     * 验证特殊值
     */
    private Boolean checkSpecial;
    
    /**
    * 特殊值值（一般未检出当0计算）
    */
    @Column(length=25)
    @Length(max = 25, message = "特殊值值长度不能超过25")
	private String specialValue;

    /**
     * 验证精密度
     */
    private Boolean checkPrecision;
    
    /**
    * 保留有效位数
    */
	private Integer effectDecimal;
    
    /**
    * 保留小数位数
    */
    @Column(nullable=false)
    @ColumnDefault("-1")
	private Integer mostDecimal= -1;

    /**
     * 是否正则处理
     */
    private Boolean valueRegular;

    /**
     * 正则内容
     */
    @Column(length=4000)
    private String valueEigen;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
	private Date modifyDate;
    
 }