package com.sinoyd.parse.entity;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * Datas实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class Datas implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Datas() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 方案Id
    */
    @Column(length=25)
	private String planId;

    /**
     * 应用id
     */
    @Column(length=25)
    private String appId;

    /**
     * 解析日志id
     */
    @Column(length=25)
    private String parselogId;
    
    /**
    * 仪器Id
    */
    @Column(length=125)
	private String instrumentId;
    
    /**
    * 仪器名称
    */
    @Column(length=125)
	private String instrumentName;
    
    /**
    * 仪器编号
    */
    @Column(length=125)
	private String instrumentCode;
    
    /**
    * 样品编号
    */
    @Column(length=125)
	private String sampleCode;
    
    /**
    * 分析项目名称
    */
    @Column(length=125)
	private String analyzeItemName;
    
    /**
    * 参数名称
    */
    @Column(length=125)
	private String paramName;
    
    /**
    * 属性别名
    */
    @Column(length=125)
	private String paramAlias;
    
    /**
    * 属性结果
    */
    @Column(length=125)
	private String value;
    
    /**
    * 数据解析时间
    */
    @Column(nullable=false)
	private Date parseDataTime;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
	private Date modifyDate;
    
    /**
    * 预留值1
    */
    @Column(length=125)
	private String extend1;
    
    /**
    * 预留值2
    */
    @Column(length=125)
	private String extend2;
    
    /**
    * 预留值3
    */
    @Column(length=125)
	private String extend3;
    
    /**
    * 预留值4
    */
    @Column(length=125)
	private String extend4;
    
    /**
    * 预留值5
    */
    @Column(length=125)
	private String extend5;
    
 }