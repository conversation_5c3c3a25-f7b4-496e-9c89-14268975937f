package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流数据实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamData implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamData() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.newId();

    /**
     * 应用id
     */
    private String appId;

    /**
     * 方案id
     */
    private String planId;

    /**
     * 解析日志主键id
     */
    private String parseStreamLogId;

    /**
     * 流水号
     */
    @Column
    private String serialNumber;

    /**
     * 仪器Id
     */
    @Column
    private String instrumentId;

    /**
     * 仪器名称
     */
    @Column
    private String instrumentName;

    /**
     * 仪器编号
     */
    @Column
    private String instrumentCode;

    /**
     * 参数名称
     */
    @Column
    private String paramName;

    /**
     * 参数别名
     */
    @Column
    private String paramAlias;

    /**
     * 参数类型 （1：样品参数 2：公共参数 3：现场值 4：电子天平
     */
    private Integer paramType;

    /**
     * 参数值
     */
    @Column
    private String value;

    /**
     * 单位
     */
    @Column
    private String unit;

    /**
     * 数据解析时间
     */
    private Date parseDataTime;

    /**
     * 组织机构Id
     */
    @Column
    private String orgId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    private Date modifyDate;

    /**
     * 预留值1
     */
    @Column
    private String extend1;

    /**
     * 预留值2
     */
    @Column
    private String extend2;

    /**
     * 预留值3
     */
    @Column
    private String extend3;

    /**
     * 预留值4
     */
    @Column
    private String extend4;

    /**
     * 预留值5
     */
    @Column
    private String extend5;
}