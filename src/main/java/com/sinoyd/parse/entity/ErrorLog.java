package com.sinoyd.parse.entity;

import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;


/**
 * ErrorLog实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 @EntityListeners(AuditingEntityListener.class)
 public  class ErrorLog implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  ErrorLog() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 解析日志主键
    */
    @Column(length=25)
	private String parselogId;
    
    /**
    * 流程Id
    */
    @Column(length=25)
	private String flowId;
    
    /**
    * 异常类型
    */
    @Column(length=25)
	private String errorType;
    
    /**
    * 异常发生时间
    */
	private Date errorTime;
    
    /**
    * 日志内容
    */
	private String logContent;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;
    
    /**
    * 创建人
    */
    @Column(length=25)
    @CreatedBy
	private String creator;
    
    /**
    * 创建日期
    */
    @CreatedDate
	private Date createDate;
    
    /**
    * 修改人
    */
    @Column(length=25)
    @LastModifiedBy
	private String modifier;
    
    /**
    * 修改日期
    */
    @LastModifiedDate
	private Date modifyDate;
    
 }