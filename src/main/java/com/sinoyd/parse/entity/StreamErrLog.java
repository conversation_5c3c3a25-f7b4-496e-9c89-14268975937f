package com.sinoyd.parse.entity;

import com.sinoyd.frame.base.entity.BaseEntity;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import com.sinoyd.frame.util.UUIDHelper;
import lombok.Data;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;
import java.util.Date;


/**
 * 解析流错误日志实体
 *
 * <AUTHOR>
 * @version V1.0.0 2021/07/21
 * @since V100R001
 */
@MappedSuperclass
@Data
@EntityListeners(AuditingEntityListener.class)
public class StreamErrLog implements BaseEntity, Serializable {

    private static final long serialVersionUID = 1L;

    public StreamErrLog() {
        this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : "";
    }

    /**
     * 主键id
     */
    @Id
    @Column
    private String id = UUIDHelper.newId();

    /**
     * 解析日志主键id
     */
    private String parseStreamLogId;

    /**
     * 异常类型
     */
    @Column
    private String errorType;

    /**
     * 异常发生时间
     */
    private Date errorTime;

    /**
     * 日志内容
     */
    private String logContent;

    /**
     * 组织机构Id
     */
    @Column
    private String orgId;

    /**
     * 是否删除
     */
    private Boolean isDeleted = false;

    /**
     * 创建人
     */
    @Column
    @CreatedBy
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @Column
    @LastModifiedBy
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    private Date modifyDate;

}