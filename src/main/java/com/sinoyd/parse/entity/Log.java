package com.sinoyd.parse.entity;


import java.io.Serializable;
import com.sinoyd.frame.base.entity.BaseEntity;
import javax.persistence.*;
import lombok.Data;
import com.sinoyd.frame.util.UUIDHelper;
import java.util.Date;
import com.sinoyd.frame.configuration.PrincipalContextUser;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;


/**
 * Log实体
 * <AUTHOR>
 * @version V1.0.0 2020/12/21
 * @since V100R001
 */
 @MappedSuperclass
 @Data
 public  class Log implements BaseEntity,Serializable {

   private static final long serialVersionUID = 1L;

    public  Log() { 
       this.orgId = PrincipalContextUser.getPrincipal() != null ? PrincipalContextUser.getPrincipal().getOrgId() : ""; 
   }

    /**
    * 主键id
    */
    @Id
    @Column(length = 50)
    private String id = UUIDHelper.newId();
    
    /**
    * 应用Id
    */
    @Column(length=25)
	private String appId;
    
    /**
    * 解析类型（1：文件解析 2：数据流解析 3:文件解析调试）
    */
    @Column(length=25)
	private String parseType;
    
    /**
    * 解析文件名(文件解析时输入)
    */
    @Column(length=250)
	private String fileOrgName;
    
    /**
    * 解析文件源目录(文件解析时输入)
    */
    @Column(length=250)
	private String fileOrgFolderName;
    
    /**
    * 解析后文件名
    */
    @Column(length=250)
	private String fileName;
    
    /**
    * 解析成功存放目录
    */
    @Column(length=250)
	private String destFolderName;
    
    /**
    * 解析开始时间
    */
	private Date beginTime;
    
    /**
    * 解析结束时间
    */
	private Date endTime;
    
    /**
    * 解析状态(1:解析中 2:解析成功 3:解析失败 )
    */
    @Column(length=25)
	private String parseStatus;
    
    /**
    * 处理方式(解析,上传)
    */
    @Column(length=25)
	private String handleType;
    
    /**
    * 日志内容
    */
	private String logContent;
    
    /**
    * 组织机构Id
    */
    @Column(length=25)
	private String orgId;
    
    /**
    * 是否删除
    */
	private Boolean isDeleted= false;


    /**
     * 创建人
     */
    @Column(length=25)
    @CreatedBy
    private String creator;

    /**
     * 创建日期
     */
    @CreatedDate
    private Date createDate;

    /**
     * 修改人
     */
    @Column(length=25)
    @LastModifiedBy
    private String modifier;

    /**
     * 修改日期
     */
    @LastModifiedDate
    private Date modifyDate;

}