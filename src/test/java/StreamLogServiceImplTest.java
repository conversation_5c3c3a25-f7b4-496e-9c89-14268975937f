//import com.sinoyd.frame.base.repository.CommonRepository;
//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.parse.criteria.StreamLogCriteria;
//import com.sinoyd.parse.dto.*;
//import com.sinoyd.parse.repository.OrgStreamRepository;
//import com.sinoyd.parse.repository.StreamAppRepository;
//import com.sinoyd.parse.repository.StreamErrLogRepository;
//import com.sinoyd.parse.service.impl.StreamLogServiceImpl;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class StreamLogServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    StreamLogServiceImpl streamLogServiceImpl;
//
//    @Mock
//    private StreamAppRepository repository;
//
//    @Mock
//    private OrgStreamRepository orgStreamRepository;
//
//    @Mock
//    private StreamErrLogRepository streamErrLogRepository;
//
//    @Mock
//    private CommonRepository commonRepository;
//
//    @Mock
//    private CommonRepository comRepository;
//
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testFindByPage() {
//
//        // 断言：streamLogServiceImpl 对象不为 null
//        Assert.assertNotNull(streamLogServiceImpl);
//
//        PageBean<DtoStreamLog> pageBean = new PageBean<>();
//        StreamLogCriteria streamLogCriteria = new StreamLogCriteria();
//        streamLogCriteria.setInstrumentNameCode("instName");
//
//        doAnswer(invocation -> {
//            PageBean<DtoStreamLog> arg2 = invocation.getArgumentAt(0, PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean, streamLogCriteria);
//
//        streamLogServiceImpl.findByPage(pageBean, streamLogCriteria);
//
//        Assert.assertNotNull(pageBean);
//
//    }
//
//    @Test
//    public void testFindSourceStream() {
//        // 断言：streamLogServiceImpl 对象不为 null
//        Assert.assertNotNull(streamLogServiceImpl);
//        String id = "";
//        DtoOrgStream dtoOrgStream = new DtoOrgStream();
//        dtoOrgStream.setAppId("1");
//
//        when(orgStreamRepository.findStreamContent(id)).thenReturn(dtoOrgStream);
//
//        DtoOrgStream data = streamLogServiceImpl.findSourceStream(id);
//        // 断言：判断成功
//        Assert.assertEquals("streamLog not match","1",data.getAppId());
//    }
//
//    @Test
//    public void testFindStreamDataErrLog() {
//
//        // 断言：streamLogServiceImpl 对象不为 null
//        Assert.assertNotNull(streamLogServiceImpl);
//        String id = "111";
//        String paramName = "aaa";
//        List<DtoStreamErrLog> streamErrLogList = new ArrayList<>();
//        DtoStreamErrLog dtostreamLog = new DtoStreamErrLog(id,null,null);
//        streamErrLogList.add(dtostreamLog);
//        List<DtoStreamData> streamDataList = new ArrayList<>();
//        StringBuilder sb = new StringBuilder();
//        Map<String, Object> values = new HashMap<>();
//
//        when(commonRepository.find(sb.toString(), values)).thenReturn(streamDataList);
//        when(streamErrLogRepository.findByParseStreamId(id)).thenReturn(streamErrLogList);
//
//        DtoStreamDataErrLog dtoStreamDataErrLog = streamLogServiceImpl.findStreamDataErrLog(id, paramName);
//        // 断言：判断成功
//        Assert.assertTrue("streamLog not match", dtoStreamDataErrLog.getStreamData().isEmpty());
//    }
//
//    @Test
//    public void testFindStreamErrLog() {
//
//        // 断言：streamLogServiceImpl 对象不为 null
//        Assert.assertNotNull(streamLogServiceImpl);
//        String id = "111";
//        DtoStreamErrLog dtoStreamErrLog = new DtoStreamErrLog(null,null,null);
//        dtoStreamErrLog.setId("111");
//
//        when(streamErrLogRepository.findErrLogDetail(id)).thenReturn(dtoStreamErrLog);
//
//        DtoStreamErrLog data = streamLogServiceImpl.findStreamErrLog(id);
//        Assert.assertEquals("streamLog not match", "111", data.getId());
//    }
//
//}
