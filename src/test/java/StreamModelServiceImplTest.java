//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.parse.criteria.StreamDataCriteria;
//import com.sinoyd.parse.criteria.StreamDataMobileCriteria;
//import com.sinoyd.parse.dto.DtoMobileStreamData;
//import com.sinoyd.parse.dto.DtoStreamData;
//import com.sinoyd.parse.dto.DtoStreamModel;
//import com.sinoyd.parse.service.impl.StreamModelServiceImpl;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//import org.springframework.data.redis.core.RedisTemplate;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class StreamModelServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    StreamModelServiceImpl streamModelService;
//
//    @Mock
//    private RedisTemplate redisTemplate;
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testSyncData() {
//
//        // 断言：streamModelService 对象不为 null
//        Assert.assertNotNull(streamModelService);
//
//        List<DtoStreamModel> streamModels = new ArrayList<>();
//
//
//        streamModelService.syncData(streamModels);
//        // 断言：判断成功
//        Assert.assertTrue("streamLog not match",streamModels.isEmpty());
//
//    }
//
//}
