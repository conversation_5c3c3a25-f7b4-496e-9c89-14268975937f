//import com.sinoyd.frame.base.repository.CommonRepository;
//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.frame.service.impl.BaseJpaServiceImpl;
//import com.sinoyd.parse.criteria.StreamConfigCriteria;
//import com.sinoyd.parse.dto.DtoStreamConfig;
//import com.sinoyd.parse.dto.DtoStreamParamConfig;
//import com.sinoyd.parse.repository.StreamAppRepository;
//import com.sinoyd.parse.repository.StreamConfigRepository;
//import com.sinoyd.parse.repository.StreamParamConfigRepository;
//import com.sinoyd.parse.service.impl.StreamConfigServiceImpl;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import java.util.*;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class StreamConfigServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    StreamConfigServiceImpl streamConfigServiceImpl;
//
//    // @Mock 注入需要的依赖对象
//    @Mock
//    private StreamConfigRepository repository;
//
//    // @Mock 注入需要的依赖对象
//    @Mock
//    private StreamParamConfigRepository streamParamConfigRepository;
//
//    // @Mock 注入需要的依赖对象
//    @Mock
//    private StreamAppRepository streamAppRepository;
//
//    @Mock
//    private CommonRepository comRepository;
//
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testFindByPage() {
//
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//
//        PageBean<DtoStreamConfig> pageBean = new PageBean<>();
//        StreamConfigCriteria streamConfigCriteria = new StreamConfigCriteria();
//        streamConfigCriteria.setPlanName("tstPlan");
//
//        doAnswer(invocation -> {
//            PageBean<DtoStreamConfig> arg2 = invocation.getArgumentAt(0,PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean,streamConfigCriteria);
//
//        streamConfigServiceImpl.findByPage(pageBean, streamConfigCriteria);
//
//        Assert.assertNotNull(pageBean);
//
//    }
//
//    @Test
//    public void testFindStreamConfig(){
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//        String id = "d8cc2e00-f406-4500-95f9-564397a98b8c";
//        DtoStreamConfig dtoStreamConfig = new DtoStreamConfig();
//        dtoStreamConfig.setPlanName("小的声级计");
//        when(repository.findOne(id)).thenReturn(dtoStreamConfig);
//        DtoStreamConfig streamConfig = streamConfigServiceImpl.findStreamConfig(id);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match","小的声级计",streamConfig.getPlanName());
//    }
//
//    @Test
//    public void testFindParamConfigByPlanId() {
//
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//        String planId = "d8cc2e00-f406-4500-95f9-564397a98b8c";
//        DtoStreamConfig dtoStreamConfig = new DtoStreamConfig();
//        List<DtoStreamParamConfig> dtoStreamParamConfigList = new ArrayList<>();
//        when(repository.findOne(planId)).thenReturn(dtoStreamConfig);
//        when(streamParamConfigRepository.findByPlanId(planId)).thenReturn(dtoStreamParamConfigList);
//        List<DtoStreamParamConfig> streamParamConfigs = streamConfigServiceImpl.findParamConfigByPlanId(planId);
//
//        Assert.assertTrue(streamParamConfigs.isEmpty());
//    }
//
//    @Test
//    public void testSaveStreamConfig() {
//
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//        DtoStreamConfig entity = new DtoStreamConfig();
//        entity.setPlanName("测试方案");
//        List<DtoStreamConfig> oriStreamConfigList = new ArrayList<>();
//        when(repository.findByPlanNameAndIsDeleted(entity.getPlanName(), false)).thenReturn(oriStreamConfigList);
//        when(repository.save(entity)).thenReturn(entity);
//
//        DtoStreamConfig data = streamConfigServiceImpl.saveStreamConfig(entity);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match","测试方案",data.getPlanName());
//    }
//
//
//    @Test
//    public void testUpdateStreamConfig() {
//
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//        String id = "d8cc2e00-f406-4500-95f9-564397a98b8c";
//        DtoStreamConfig entity = new DtoStreamConfig();
//        entity.setPlanName("修改名称");
//        entity.setId(id);
//        DtoStreamConfig oriStreamConfig = new DtoStreamConfig();
//        oriStreamConfig.setPlanName("原名称");
//        oriStreamConfig.setIsDeleted(false);
//        List<DtoStreamConfig> streamConfigList = new ArrayList<>();
//
//        when(repository.findOne(id)).thenReturn(oriStreamConfig);
//        when(repository.findByPlanNameAndIsDeleted(entity.getPlanName(), false)).thenReturn(streamConfigList);
//        when(repository.save(entity)).thenReturn(entity);
//
//        DtoStreamConfig data = streamConfigServiceImpl.updateStreamConfig(entity);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match","修改名称",data.getPlanName());
//    }
//
//    @Test
//    public void testDeleteByIds() {
//
//        // 断言：streamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamConfigServiceImpl);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("d8cc2e00-f406-4500-95f9-564397a98b8c");
//        ids.add("2a3bb6b3-c3f1-4444-bf1d-f149e63abda5");
//        int delCnt = 0;
////        when(streamParamConfigRepository.logicDeleteByPlanId(ids, true)).thenReturn(oriStreamConfig);
////        when(streamAppRepository.logicDeleteByPlanId(ids, true)).thenReturn(streamConfigList);
//        when(repository.logicDeleteById(ids, new Date())).thenReturn(delCnt);
//
//        int cnt = streamConfigServiceImpl.deleteByIds(ids);
//        // 断言：判断成功
//        Assert.assertTrue("streamConfig not match",cnt == 0);
//    }
//
//}
