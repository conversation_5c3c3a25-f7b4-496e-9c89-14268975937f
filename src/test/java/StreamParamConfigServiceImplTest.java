//import com.sinoyd.frame.base.repository.CommonRepository;
//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.parse.criteria.StreamParamConfigCriteria;
//import com.sinoyd.parse.dto.DtoStreamConfig;
//import com.sinoyd.parse.dto.DtoStreamParamConfig;
//import com.sinoyd.parse.repository.StreamConfigRepository;
//import com.sinoyd.parse.repository.StreamParamConfigRepository;
//import com.sinoyd.parse.service.impl.StreamParamConfigServiceImpl;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class StreamParamConfigServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    StreamParamConfigServiceImpl streamParamConfigServiceImpl;
//
//    // @Mock 注入需要的依赖对象
//    @Mock
//    private StreamParamConfigRepository repository;
//
//    // @Mock 注入需要的依赖对象
//    @Mock
//    private StreamConfigRepository streamConfigRepository;
//
//    @Mock
//    private CommonRepository comRepository;
//
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testFindByPage() {
//
//        // 断言：streamParamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamParamConfigServiceImpl);
//
//        PageBean<DtoStreamParamConfig> pageBean = new PageBean<>();
//        StreamParamConfigCriteria streamParamConfigCriteria = new StreamParamConfigCriteria();
//        streamParamConfigCriteria.setParamName("tstParam");
//        streamParamConfigCriteria.setPlanId("d8cc2e00-f406-4500-95f9-564397a98b8c");
//
//        doAnswer(invocation -> {
//            PageBean<DtoStreamParamConfig> arg2 = invocation.getArgumentAt(0, PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean, streamParamConfigCriteria);
//
//        streamParamConfigServiceImpl.findByPage(pageBean, streamParamConfigCriteria);
//
//        Assert.assertNotNull(pageBean);
//
//    }
//
//    @Test
//    public void testFindStreamParamConfig() {
//        // 断言：streamParamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamParamConfigServiceImpl);
//        String id = "b2e27f86-c9ff-43fc-917b-4c33fdc4a3ed";
//        DtoStreamParamConfig dtoStreamParamConfig = new DtoStreamParamConfig();
//        dtoStreamParamConfig.setParamName("测试参数");
//
//        when(repository.findOne(id)).thenReturn(dtoStreamParamConfig);
//
//        DtoStreamParamConfig streamParamConfig = streamParamConfigServiceImpl.findStreamParamConfig(id);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match", "测试参数", streamParamConfig.getParamName());
//    }
//
//
//    @Test
//    public void testSaveStreamParamConfig() {
//
//        // 断言：streamParamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamParamConfigServiceImpl);
//        DtoStreamParamConfig entity = new DtoStreamParamConfig();
//        entity.setParamName("测试参数");
//        entity.setPlanId("d8cc2e00-f406-4500-95f9-564397a98b8c");
//        DtoStreamConfig dtoStreamConfig = new DtoStreamConfig();
//        dtoStreamConfig.setIsDeleted(false);
//        List<DtoStreamParamConfig> oriStreamParamConfigList = new ArrayList<>();
//
//        when(streamConfigRepository.findOne(entity.getPlanId())).thenReturn(dtoStreamConfig);
//        when(repository.findByPlanIdAndParamNameAndIsDeleted(entity.getPlanId(), entity.getParamName(), false)).thenReturn(oriStreamParamConfigList);
//        when(repository.save(entity)).thenReturn(entity);
//
//        DtoStreamParamConfig data = streamParamConfigServiceImpl.saveStreamParamConfig(entity);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match", "测试参数", data.getParamName());
//    }
//
//    //
////
//    @Test
//    public void testUpdateStreamParamConfig() {
//
//        // 断言：streamParamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamParamConfigServiceImpl);
//        String id = "b2e27f86-c9ff-43fc-917b-4c33fdc4a3ed";
//        DtoStreamParamConfig entity = new DtoStreamParamConfig();
//        entity.setParamName("修改参数名称");
//        entity.setId(id);
//        DtoStreamParamConfig oriStreamParamConfig = new DtoStreamParamConfig();
//        oriStreamParamConfig.setParamName("原参数名称");
//        oriStreamParamConfig.setIsDeleted(false);
//        List<DtoStreamParamConfig> streamParamConfigList = new ArrayList<>();
//
//        when(repository.findOne(id)).thenReturn(oriStreamParamConfig);
//        when(repository.findByPlanIdAndParamNameAndIsDeleted(entity.getPlanId(), entity.getParamName(), false)).thenReturn(streamParamConfigList);
//        when(repository.save(entity)).thenReturn(entity);
//
//        DtoStreamParamConfig data = streamParamConfigServiceImpl.updateStreamParamConfig(entity);
//        // 断言：判断成功
//        Assert.assertEquals("streamConfig not match", "修改参数名称", data.getParamName());
//    }
//
//    @Test
//    public void testDeleteByIds() {
//
//        // 断言：streamParamConfigServiceImpl 对象不为 null
//        Assert.assertNotNull(streamParamConfigServiceImpl);
//
//        List<String> ids = new ArrayList<>();
//        ids.add("b2e27f86-c9ff-43fc-917b-4c33fdc4a3ed");
//        ids.add("4b2b7b31-cec9-4913-bedc-3f1e0e136279");
//        int delCnt = 0;
//        when(repository.logicDeleteById(ids, new Date())).thenReturn(delCnt);
//
//        int cnt = streamParamConfigServiceImpl.deleteByIds(ids);
//        // 断言：判断成功
//        Assert.assertTrue("streamConfig not match",cnt == 0);
//    }
//
//}
