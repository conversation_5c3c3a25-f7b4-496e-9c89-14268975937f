//import com.sinoyd.frame.base.repository.CommonRepository;
//import com.sinoyd.frame.base.util.PageBean;
//import com.sinoyd.parse.criteria.StreamDataCriteria;
//import com.sinoyd.parse.criteria.StreamDataMobileCriteria;
//import com.sinoyd.parse.dto.DtoMobileStreamData;
//import com.sinoyd.parse.dto.DtoOrgStream;
//import com.sinoyd.parse.dto.DtoStreamData;
//import com.sinoyd.parse.repository.StreamDataRepository;
//import com.sinoyd.parse.service.impl.StreamDataServiceImpl;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import org.mockito.runners.MockitoJUnitRunner;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.Mockito.doAnswer;
//import static org.mockito.Mockito.when;
//
//@RunWith(MockitoJUnitRunner.class)
//public class StreamDataServiceImplTest {
//
//    // @InjectMock 注入被测对象, 一般写在最前面
//    @InjectMocks
//    StreamDataServiceImpl streamDataServiceImpl;
//
//    @Mock
//    private StreamDataRepository repository;
//
//    @Mock
//    private CommonRepository commonRepository;
//
//    @Mock
//    private CommonRepository comRepository;
//
//
//    // 执行测试之前执行
//    @Before
//    public void setup() {
//        // 初始化 mock 注入环境
//        MockitoAnnotations.initMocks(this);
//    }
//
//    @Test
//    public void testFindByPage() {
//
//        // 断言：streamDataServiceImpl 对象不为 null
//        Assert.assertNotNull(streamDataServiceImpl);
//
//        PageBean<DtoStreamData> pageBean = new PageBean<>();
//        StreamDataCriteria streamDataCriteria = new StreamDataCriteria();
//        streamDataCriteria.setInstrumentNameCode("instName");
//
//        doAnswer(invocation -> {
//            PageBean<DtoStreamData> arg2 = invocation.getArgumentAt(0, PageBean.class);
//            arg2.setData(new ArrayList<>());
//            return null;
//        }).when(comRepository).findByPage(pageBean, streamDataCriteria);
//
//        streamDataServiceImpl.findByPage(pageBean, streamDataCriteria);
//
//        Assert.assertNotNull(pageBean);
//
//    }
//
//    @Test
//    public void testFindMobileStreamData() {
//        // 断言：streamDataServiceImpl 对象不为 null
//        Assert.assertNotNull(streamDataServiceImpl);
//        StringBuilder sb = new StringBuilder();
//        StreamDataMobileCriteria streamDataMobileCriteria = new StreamDataMobileCriteria();
//        List<DtoStreamData> dtoStreamDataList = new ArrayList<>();
//
//        when(commonRepository.find(sb.toString(), streamDataMobileCriteria.getValues())).thenReturn(dtoStreamDataList);
//
//        List<DtoMobileStreamData> dtoMobileStreamDataList = streamDataServiceImpl.findMobileStreamData(streamDataMobileCriteria);
//        // 断言：判断成功
//        Assert.assertTrue("streamLog not match",dtoMobileStreamDataList.isEmpty());
//    }
//
//}
